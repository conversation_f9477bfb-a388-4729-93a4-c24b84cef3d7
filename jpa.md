# Análisis de Optimización - Ggnf0100.java

## Resumen Ejecutivo

INDICES
Claro. Aquí tienes la explicación de cada índice de la tabla `GFVTCNA0` en el formato solicitado.

---

### Análisis de Índices para la Tabla `NPMPR.GFVTCNA0`

| Nombre del Índice (INDNAME) | Tipo de Índice | Columnas Indexadas (COLNAMES) | Propósito y Explicación |
| :--- | :--- | :--- | :--- |
| **`GFVKCNA0`** | **Clave Primaria (Primary Key)** | `+AGT_IDR+EMP_CVE+CNA_NUM` | **Garantiza la unicidad** de cada fila en la tabla. La combinación de estos tres campos no puede repetirse. Es el identificador principal y el método de acceso más rápido a una fila específica. |
| `GFVICNA1` | No Único (Permite Duplicados) | `+MDE_CVE` | Acelera las búsquedas y ordenamientos (`WHERE`, `ORDER BY`) que se basan en la columna `MDE_CVE`. |
| `GFVICNA10` | No Único (Permite Duplicados) | `+CPG_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `CPG_CVE`. |
| `GFVICNA11` | No Único (Permite Duplicados) | `+PPG_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `PPG_CVE`. |
| `GFVICNA12` | No Único (Permite Duplicados) | `+EMP_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `EMP_CVE`. |
| `GFVICNA13` | No Único (Permite Duplicados) | `+AGT_IDR` | Acelera las búsquedas y ordenamientos que se basan en la columna `AGT_IDR`. |
| `GFVICNA14` | No Único (Permite Duplicados) | `+ZEC_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `ZEC_CVE`. |
| `GFVICNA15` | No Único (Permite Duplicados) | `+CNA_GER_ZON_ENV` | Acelera las búsquedas y ordenamientos que se basan en la columna `CNA_GER_ZON_ENV`. |
| `GFVICNA2` | No Único (Permite Duplicados) | `+MTE_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `MTE_CVE`. |
| `GFVICNA3` | No Único (Permite Duplicados) | `+CTB_NUM_CTA` | Acelera las búsquedas y ordenamientos que se basan en la columna `CTB_NUM_CTA`. |
| `GFVICNA4` | No Único (Permite Duplicados) | `+AGT_IDR_RCL` | Acelera las búsquedas y ordenamientos que se basan en la columna `AGT_IDR_RCL`. |
| `GFVICNA5` | No Único (Permite Duplicados) | `+TFP_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `TFP_CVE`. |
| `GFVICNA6` | No Único (Permite Duplicados) | `+GNA_CVE_AGT+GNA_CVE` | Optimiza consultas que filtran o agrupan usando las columnas `GNA_CVE_AGT` y `GNA_CVE` **conjuntamente**. |
| `GFVICNA7` | No Único (Permite Duplicados) | `+TAA_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `TAA_CVE`. |
| `GFVICNA8` | No Único (Permite Duplicados) | `+OFN_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `OFN_CVE`. |
| `GFVICNA9` | No Único (Permite Duplicados) | `+CDE_CVE` | Acelera las búsquedas y ordenamientos que se basan en la columna `CDE_CVE`. |
| `IDX_GFVTCNA0_AGT_CNA` | No Único (Permite Duplicados) | `+AGT_IDR+CNA_NUM` | Optimiza consultas que filtran o agrupan usando las columnas `AGT_IDR` y `CNA_NUM` **conjuntamente**. |

---

### Resumen Arquitectónico

* **Clave Primaria (`UNIQUERULE = 'P'`)**: Solo hay una, `GFVKCNA0`, que define la identidad única de cada registro en la tabla `GFVTCNA0`.
* **Índices de Rendimiento (`UNIQUERULE = 'D'`)**: Todos los demás índices existen con el único propósito de **optimizar el rendimiento de las consultas**. La gran cantidad de índices sobre columnas individuales sugiere que la tabla `GFVTCNA0` es consultada frecuentemente usando muchos campos diferentes como criterio de filtro.

El programa `Ggnf0100.java` presenta un problema de rendimiento debido a la ejecución excesiva de consultas SQL a la tabla `GFVTDRC0`. Se ejecuta una consulta SELECT por cada folio procesado, lo que puede resultar en miles de consultas individuales.

## Flujo del Programa

### Estructura de Procesamiento Jerárquico

```
start()
└── p2000Proceso() [Loop: todos los agentes]
    ├── p2100InicioGfvtagt0() - Abre cursor GFVTAGT0
    ├── p3000ProcesoAgentes() [Loop: cada agente]
    │   ├── p7700InicioGfvtcna0() - Abre cursor GFVTCNA0
    │   ├── p3200ProcesoGfvtcna0() [Loop: contratos del agente]
    │   │   ├── p7800InicioGfvtfla0() - Abre cursor GFVTFLA0
    │   │   ├── p3300ProcesoGfvtfla0() [Loop: folios del contrato]
    │   │   │   ├── p3400RegresoCamposNac() o p3400RegresoCamposProv()
    │   │   │   ├── p3450RegresoResto() ⚠️ **AQUÍ ESTÁ EL PROBLEMA**
    │   │   │   │   └── p8100LeerGfvtdrc0() - SELECT individual a GFVTDRC0
    │   │   │   └── maestro.write(regMaestro)
    │   │   └── p7800CerrarGfvtfla0()
    │   └── p7700CerrarGfvtcna0()
    └── p2100CerrarGfvtagt0()
```

### Detalle del Problema

#### Ubicación del Problema
- **Clase**: `Ggnf0100.java`
- **Método**: `p3450RegresoResto()` (línea ~1347)
- **Llamada problemática**: `p8100LeerGfvtdrc0()` 

#### Método p8100LeerGfvtdrc0()
```java
void p8100LeerGfvtdrc0() {
    // Ejecuta un SELECT directo a GFVTDRC0
    gfvtdrc0Model = gfvtdrc0Port.select(
        Gfvtdrc0Model.builder()
            .agtIdr(toHostInt(dclgfvtagt0.getAgtIdr().toInt()))
            .cnaNum(toHostInt(dclgfvtcna0.getCnaNum().toInt()))
        .build());
    
    // Procesa el resultado
    if(gfvtdrc0Model.isPresent()) {
        // Carga los datos en dclgfvtdrc0
    }
    
    // Verifica el resultado
    db2check();
    if (this.isDb2Ok()) {
        wsIndicadores.setWsFinDrc(0);
    } else if (this.isDb2Notfnd()) {
        wsIndicadores.setWsFinDrc(1);
    } else {
        p9999AnalizaSql();
    }
}
```

#### Uso del Resultado
El resultado de la consulta se usa únicamente para determinar valores de `PkFuente`, `PkGrupo` y `PkSubgrupo`:

```java
if (wsIndicadores.getWsFinDrc() == 0) {
    regMaestro.setPkFuente(1);
    regMaestro.setPkGrupo(1);
    regMaestro.setPkSubgrupo(1);
} else {
    regMaestro.setPkFuente(1);
    regMaestro.setPkGrupo(1);
    regMaestro.setPkSubgrupo(2);  // Solo cambia PkSubgrupo
}
```

## Análisis del Impacto

### Volumen de Consultas
- **Por cada agente**: 1 cursor abierto (GFVTAGT0)
- **Por cada contrato**: 1 cursor abierto (GFVTCNA0)
- **Por cada folio**: 1 cursor abierto (GFVTFLA0) + **1 SELECT individual (GFVTDRC0)** ⚠️

### Ejemplo de Impacto
Si procesamos:
- 100 agentes
- 10 contratos por agente (promedio)
- 5 folios por contrato (promedio)

Total de consultas SELECT individuales a GFVTDRC0: **5,000 consultas**

## Optimizaciones Propuestas

### 1. Usar Cursor en lugar de SELECT Individual (Recomendada)

**Implementación**: Crear un cursor `gfvcdrc0Cursor` similar a los otros cursores del programa.

**Ventajas**:
- Consistente con el patrón del programa
- Reduce el número de round-trips a la base de datos
- Mejor rendimiento para grandes volúmenes

**Cambios necesarios**:
1. Modificar `p7950InicioGfvcdrc0()` para abrir un cursor con los registros del contrato actual
2. Modificar `p8100LeerGfvtdrc0()` para usar `cursor.next()` en lugar de `select()`
3. Agregar lógica para cerrar el cursor cuando termine el procesamiento del contrato

### 2. Cache de Resultados por Contrato

**Implementación**: Mantener un mapa con los resultados de GFVTDRC0 por combinación AGT_IDR + CNA_NUM.

**Ventajas**:
- Evita consultas repetidas para el mismo contrato
- Útil si hay múltiples folios por contrato

**Código propuesto**:
```java
private Map<String, Boolean> gfvtdrc0Cache = new HashMap<>();

void p8100LeerGfvtdrc0() {
    String key = dclgfvtagt0.getAgtIdr() + "-" + dclgfvtcna0.getCnaNum();
    
    if (gfvtdrc0Cache.containsKey(key)) {
        wsIndicadores.setWsFinDrc(gfvtdrc0Cache.get(key) ? 0 : 1);
        return;
    }
    
    // Ejecutar consulta solo si no está en cache
    // ... código actual ...
    
    // Guardar en cache
    gfvtdrc0Cache.put(key, wsIndicadores.getWsFinDrc() == 0);
}
```

### 3. Consulta Batch por Contrato

**Implementación**: Ejecutar una sola consulta que traiga todos los registros de GFVTDRC0 para un contrato.

**Ventajas**:
- Una consulta por contrato en lugar de una por folio
- Reduce significativamente el número de consultas

### 4. Análisis de Necesidad Real

**Observación importante**: El resultado de la consulta solo afecta el valor de `PkSubgrupo` (1 o 2).

**Preguntas a considerar**:
- ¿Es realmente necesario consultar GFVTDRC0 para cada folio?
- ¿El valor es el mismo para todos los folios de un contrato?
- ¿Se podría determinar este valor de otra forma?

## Recomendación Principal

Implementar la **Opción 1 (Usar Cursor)** combinada con la **Opción 2 (Cache)** para obtener el mejor rendimiento:

1. Abrir un cursor al inicio del procesamiento de cada contrato
2. Cachear el resultado para evitar múltiples lecturas del cursor
3. Cerrar el cursor al finalizar el contrato

Esto reduciría las consultas de 5,000 a aproximadamente 1,000 (una por contrato), representando una mejora del 80% en el número de consultas.

## Métricas de Mejora Esperadas

- **Reducción de consultas**: 80-90%
- **Mejora en tiempo de ejecución**: 50-70% (dependiendo de la latencia de red)
- **Menor carga en la base de datos**: Significativa
- **Mejor escalabilidad**: El programa podrá manejar mayores volúmenes sin degradación proporcional

## Próximos Pasos

1. Validar con el equipo de negocio si el valor de GFVTDRC0 es constante por contrato
2. Implementar la solución de cursor + cache
3. Realizar pruebas de rendimiento comparativas
4. Monitorear el impacto en producción