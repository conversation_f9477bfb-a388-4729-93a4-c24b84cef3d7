spring:
  datasource:
    url: *******************************************************;
    user: db2inst1
    password: gnppassword$1
  batch:
    jdbc:
      initialize-schema: always
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
context:
  sort-program-name: docker exec ttsort-container /app/ttsortx64
os390:
  base-path: /tmp/gnp/cuentacorriente
logging:
  level:
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql: TRACE