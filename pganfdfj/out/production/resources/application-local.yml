spring:
  datasource:
    # url: *******************************************************;
    url: ************************************************************;
    user: db2inst1
    password: db2inst1
    # password: gnppassword$1
  batch:
    jdbc:
      initialize-schema: always
context:
  sort-program-name: docker exec ttsort-container /app/ttsortx64
os390:
  base-path: /tmp/gnp/cuentacorriente
caravel:
  support:
    bucket:
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"