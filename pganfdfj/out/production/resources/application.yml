application:
  title: Program pganfdfj
  description: |
    ### pganfdfj in "Hexagonal Architecture"
  version: 1.0.0.SNAPSHOT

spring:
  main:
    web-application-type: NONE
  datasource:
    url: jdbc:db2://************:25010/GNPDB296:currentSchema=NPMPR;
    user: db2inst1
    password: gnppassword$1
context:
  sort-program-name: /opt/app/ttsortx64
os390:
  base-path: /nfs
  data-path: ${os390.base-path}/data

caravel:
  support:
    bucket:
      directory: ${os390.data-path}
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - 'PPCQS.NFDFJ00.EANFDFJ0'
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - 'PPCDS.KCDPP710.GFVTAGTH'
          - 'PPCDS.KCDPP710.GFVTCNAH'
          - 'PPCDS.KCDPP710.GFVTDMAH'
          - 'PPCDS.KCDPP710.GFVTCMAH'
          - 'PPCDS.KCDPP600.GFVTDRC0'


logging:
  level:
    root: INFO
    com.gnp: INFO
    org.springframework.batch: INFO
    org.springframework.jdbc: INFO
    org.springframework.transaction: INFO
    org.hibernate: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n"
server:
  port: 8080
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
info:
  name: ${application.title}
  description: ${application.description}
  version: 1.0.0.SNAPSHOT
  spring-boot-version: ${spring-boot-version}
  spring-version: ${spring-version}
  java-version: ${java-version}
  dev-team:
    name: Base100
    mail: <EMAIL>
    repo: https://github.com/base100/gke-gnp-cuentacorriente-catalogo_agentes
  build:
    date: ${build-date}
    commit:
      id: ${build-commit-id}
      message: ${build-commit-message}
      time: ${build-commit-time}
      user: ${build-commit-user}
      branch: ${build-branch}
    host: ${build-host}
    name: ${build-name}
    version: ${build-version}
    group: ${build-group}
    artifact: ${build-artifact}
    time: ${build-time}
    user: ${build-user}
    java:
      version: ${build-java-version}
      vendor: ${build-java-vendor}
      sourceCompatibility: ${build-java-sourceCompatibility}
      targetCompatibility: ${build-java-targetCompatibility}
    os:
      name: ${build-os-name}
      version: ${build-os-version}
      arch: ${build-os-arch}
    git:
      branch: ${git.branch}
      commit:
        id: ${git.commit.id}
        time: ${git.commit.time}
        user:
          name: ${git.commit.user.name}
          email: ${git.commit.user.email}
        message:
          short: ${git.commit.message.short}
          full: ${git.commit.message.full}
      build:
        time: ${git.build.time}
        version: ${git.build.version}
        user:
          name: ${git.build.user.name}
          email: ${git.build.user.email}
      dirty: ${git.dirty}
      tags: ${git.tags}
      total:
        commit:
          count: ${git.total.commit.count}
      closest:
        tag:
          name: ${git.closest.tag.name}
          commit:
            count: ${git.closest.tag.commit.count}
      local:
        branch:
          ahead: ${git.local.branch.ahead}
          behind: ${git.local.branch.behind}