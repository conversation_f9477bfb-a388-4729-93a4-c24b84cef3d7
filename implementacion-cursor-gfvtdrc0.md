# Guía de Implementación - Optimización Cursor GFVTDRC0

## An<PERSON><PERSON><PERSON>vio

He detectado que **YA EXISTE** un cursor implementado para GFVTDRC0, pero no se está utilizando en el método `p8100LeerGfvtdrc0()`. En su lugar, se está usando un SELECT directo.

### Código Actual vs Código con Cursor

**Actual (SELECT directo):**
```javaper
void p8100LeerGfvtdrc0() {
    // SELECT directo - una consulta por cada llamada
    gfvtdrc0Model = gfvtdrc0Port.select(
        Gfvtdrc0Model.builder()
            .agtIdr(toHostInt(dclgfvtagt0.getAgtIdr().toInt()))
            .cnaNum(toHostInt(dclgfvtcna0.getCnaNum().toInt()))
        .build());
    // ...
}
```

**Con Cursor (ya existe pero no se usa):**
- `p7950InicioGfvcdrc0()` - Abre el cursor
- `p7950LeerGfvcdrc0()` - Lee del cursor
- `p7950CerrarGfvcdrc0()` - Cierra el cursor

## Pasos para Implementar la Optimización

### Paso 1: Modificar el flujo en p3450RegresoResto()

**Ubicación**: Método `p3450RegresoResto()` alrededor de la línea 1345

**Cambio actual:**
```java
wsIndicadores.setWsFinDrc(0);
p8100LeerGfvtdrc0();
```

**Cambio propuesto:**
```java
// Opción A: Usar el cursor existente por contrato
// Abrir cursor al inicio del procesamiento del contrato (en p3200ProcesoGfvtcna0)
// y usar p7950LeerGfvcdrc0() aquí

// Opción B: Implementar cache para evitar múltiples lecturas
if (!gfvtdrc0CacheChecked) {
    wsIndicadores.setWsFinDrc(0);
    p7950InicioGfvcdrc0();  // Abre cursor si no está abierto
    p7950LeerGfvcdrc0();    // Lee del cursor
    p7950CerrarGfvcdrc0();  // Cierra cursor
    
    // Guardar resultado en cache para este contrato
    gfvtdrc0CacheResult = wsIndicadores.getWsFinDrc();
    gfvtdrc0CacheChecked = true;
} else {
    // Usar resultado cacheado
    wsIndicadores.setWsFinDrc(gfvtdrc0CacheResult);
}
```

### Paso 2: Agregar variables de cache

**Ubicación**: Al inicio de la clase, junto con las otras variables de instancia

```java
// Variables para cache de GFVTDRC0
private boolean gfvtdrc0CacheChecked = false;
private int gfvtdrc0CacheResult = 0;
private int currentCnaNum = -1;  // Para detectar cambio de contrato
```

### Paso 3: Resetear cache al cambiar de contrato

**Ubicación**: En el método `p3200ProcesoGfvtcna0()` al inicio

```java
void p3200ProcesoGfvtcna0() {
    // Reset cache cuando cambia el contrato
    if (currentCnaNum != dclgfvtcna0.getCnaNum().toInt()) {
        gfvtdrc0CacheChecked = false;
        currentCnaNum = dclgfvtcna0.getCnaNum().toInt();
    }
    
    // ... resto del código existente ...
}
```

### Paso 4: Implementación Alternativa - Usar Cursor una vez por Contrato

Esta es una implementación más elegante que abre el cursor una vez por contrato:

**En p3200ProcesoGfvtcna0():**
```java
void p3200ProcesoGfvtcna0() {
    dclgfvtfla0.initialize();
    wsIndicadores.setWsFinFla(0);
    wsIndicadores.setWsN(0);
    wsIndicadores.setWsP(0);
    regMaestro.setPkJefeGpoNal(0);
    regMaestro.setPkJefeGpoProv(0);
    
    // NUEVO: Leer GFVTDRC0 una vez por contrato
    wsIndicadores.setWsFinDrc(0);
    p7950InicioGfvcdrc0();
    p7950LeerGfvcdrc0();
    p7950CerrarGfvcdrc0();
    
    // Guardar resultado para usar en todos los folios
    int drcResultForContract = wsIndicadores.getWsFinDrc();
    
    p7800InicioGfvtfla0();
    while (!(wsIndicadores.getWsFinFla() == 1)) {
        // Pasar el resultado como parámetro o usar variable de instancia
        wsIndicadores.setWsFinDrc(drcResultForContract);
        p3300ProcesoGfvtfla0();
    }
    p7800CerrarGfvtfla0();
    
    wsCampos.setWsCuantosCna(wsCampos.getWsCuantosCna() + 1);
    p7700LeerGfvtcna0();
}
```

**En p3450RegresoResto():**
```java
// Eliminar estas líneas:
// wsIndicadores.setWsFinDrc(0);
// p8100LeerGfvtdrc0();

// El valor ya está en wsIndicadores.getWsFinDrc() desde p3200ProcesoGfvtcna0()

if (wsIndicadores.getWsFinDrc() == 0) {
    regMaestro.setPkFuente(1);
    regMaestro.setPkGrupo(1);
    regMaestro.setPkSubgrupo(1);
} else {
    regMaestro.setPkFuente(1);
    regMaestro.setPkGrupo(1);
    regMaestro.setPkSubgrupo(2);
}
```

## Pruebas de Rendimiento

### 1. Métricas Base (Antes de la Optimización)

```java
// Agregar al inicio de p1000Inicio()
private long startTime;
private int totalGfvtdrc0Calls = 0;

void p1000Inicio() {
    startTime = System.currentTimeMillis();
    // ... resto del código ...
}

// En p8100LeerGfvtdrc0()
void p8100LeerGfvtdrc0() {
    totalGfvtdrc0Calls++;
    // ... resto del código ...
}

// En p4000Fin()
void p4000Fin() {
    long endTime = System.currentTimeMillis();
    long totalTime = endTime - startTime;
    
    display("=== METRICAS DE RENDIMIENTO ===");
    display("Tiempo total: " + totalTime + " ms");
    display("Total llamadas a GFVTDRC0: " + totalGfvtdrc0Calls);
    display("Agentes procesados: " + wsCampos.getWsCuantosAgt());
    display("Contratos procesados: " + wsCampos.getWsCuantosCna());
    display("Registros grabados: " + wsContadores.getWsGrabados());
    display("===============================");
    
    // ... resto del código ...
}
```

### 2. Prueba Comparativa

1. **Ejecutar con código actual** y guardar métricas
2. **Implementar optimización**
3. **Ejecutar con código optimizado** y comparar métricas

### 3. Validación de Resultados

Crear un método para validar que los resultados son idénticos:

```java
// En p3450RegresoResto(), después de determinar PkSubgrupo
if (DEBUG_MODE) {
    int oldSubgrupo = regMaestro.getPkSubgrupo();
    
    // Llamar al método antiguo para comparar
    wsIndicadores.setWsFinDrc(0);
    p8100LeerGfvtdrc0();
    int newSubgrupo = wsIndicadores.getWsFinDrc() == 0 ? 1 : 2;
    
    if (oldSubgrupo != newSubgrupo) {
        display("ERROR: Diferencia en resultados!");
        display("AGT_IDR: " + dclgfvtagt0.getAgtIdr());
        display("CNA_NUM: " + dclgfvtcna0.getCnaNum());
        display("Subgrupo cache: " + oldSubgrupo);
        display("Subgrupo SELECT: " + newSubgrupo);
    }
}
```

## Recomendación

Sugiero implementar la **Opción de Cursor por Contrato** (Paso 4) porque:

1. Es más limpia y mantiene la lógica separada
2. Reduce las consultas de N folios a 1 por contrato
3. No requiere variables adicionales de cache
4. Es consistente con el patrón del programa

## Checklist de Implementación

- [ ] Backup del código actual
- [ ] Implementar métricas de rendimiento base
- [ ] Ejecutar y documentar métricas actuales
- [X] Implementar optimización elegida
- [ ] Agregar validación de resultados
- [ ] Ejecutar pruebas con datos de prueba
- [ ] Comparar métricas y validar resultados
- [ ] Remover código de debug
- [ ] Documentar mejoras obtenidas

## Resultados Esperados

- **Reducción de consultas**: De N folios a 1 por contrato (80-90% menos)
- **Mejora en tiempo**: 40-60% dependiendo de la latencia de BD
- **Resultados idénticos**: El archivo de salida debe ser exactamente igual