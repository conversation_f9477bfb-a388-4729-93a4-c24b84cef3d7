plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management'
}

dependencies {
    implementation project(':catalogo-agentes-business-spring-boot-starter')

    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-batch'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation "mx.com.gnp.cuentacorriente:batch-spring-boot-starter:${gnpCuentaCorrienteCommonVersion}"

    implementation fileTree(dir: '../caravel-support', include: '*.jar')
}

bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
