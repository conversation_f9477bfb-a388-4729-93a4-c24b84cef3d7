plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management'
    id 'io.freefair.aspectj.post-compile-weaving' version '8.4'
}

dependencies {

    implementation("org.springframework.boot:spring-boot-starter-aop")
    implementation project(':catalogo-agentes-business-spring-boot-starter')

    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-batch'
    implementation "mx.com.gnp.cuentacorriente:batch-spring-boot-starter:${gnpCuentaCorrienteCommonVersion}"

/*
    implementation fileTree(dir: '../caravel-support', include: '*.jar')

 */
    implementation("com.base100.caravel.support:os390-spring-batch:6.0.0-SNAPSHOT")
}

bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

bootRun {
    jvmArgs = [
        "-Xmx2g",
        "-Xms512m",
        "-XX:MaxMetaspaceSize=256m"
    ]
}
