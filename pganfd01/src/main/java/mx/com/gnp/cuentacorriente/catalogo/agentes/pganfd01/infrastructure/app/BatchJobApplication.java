package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.app;

import mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config.JobConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * The type Application.
 */
@SpringBootApplication
@Import(JobConfig.class)
@ComponentScan(basePackages = {"mx.com.gnp.cuentacorriente", "com.base100.caravel.support"})
@EnableAspectJAutoProxy
public class BatchJobApplication {
    public static void main(final String[] args) {
        System.exit(SpringApplication.exit(SpringApplication.run(BatchJobApplication.class, args)));
    }
}
