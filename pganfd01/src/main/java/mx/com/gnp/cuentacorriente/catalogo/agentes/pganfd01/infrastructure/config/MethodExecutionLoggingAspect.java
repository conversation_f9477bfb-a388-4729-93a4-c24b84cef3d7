package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicLong;

@Aspect
@Component
public class MethodExecutionLoggingAspect {

    private static final Logger log = LoggerFactory.getLogger(MethodExecutionLoggingAspect.class);

    // Variables para CSV de métricas
    private static final String CSV_FILE = "pganfd01/logs/query-performance-metrics.csv";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private final AtomicLong executionCounter = new AtomicLong(0);
    private volatile boolean csvHeaderWritten = false;

    @Pointcut("(execution(* mx.com.gnp.cuentacorriente..*.*(..)) || execution(* com.base100.caravel.support..*.*(..))) && !within(mx.com.gnp.cuentacorriente.infraestructura.config.aop..*)")
    public void applicationPackagePointcut() {
        // Pointcut definition for application packages
    }

    @Around("applicationPackagePointcut()")
    public Object logMethodExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        // Captura del tiempo de inicio con mayor precisión
        long startTime = System.nanoTime();
        LocalDateTime timestamp = LocalDateTime.now();
        long executionId = executionCounter.incrementAndGet();

        Object[] methodArgs = joinPoint.getArgs();

        if (log.isDebugEnabled()) {
            log.debug(">> Enter: {} with args = {}", methodName, Arrays.toString(methodArgs));
        }

        Object result;
        boolean hasException = false;
        String exceptionType = "";

        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            hasException = true;
            exceptionType = throwable.getClass().getSimpleName();

            long executionTimeNs = System.nanoTime() - startTime;
            double executionTimeMs = executionTimeNs / 1_000_000.0;

            log.error("!! Exception in {} after {}ms: {} with cause: {}", methodName,
                String.format(Locale.US, "%.3f", executionTimeMs),
                throwable.getClass().getSimpleName(), throwable.getMessage());

            // Escribir excepción al CSV
            writeToCSV(executionId, timestamp, className, methodName, executionTimeMs,
                      hasException, exceptionType, "EXCEPTION");

            throw throwable;
        }

        // Cálculo del tiempo de ejecución
        long executionTimeNs = System.nanoTime() - startTime;
        double executionTimeMs = executionTimeNs / 1_000_000.0;

        // Determinar tipo de operación
        String operationType = determineOperationType(methodName);

        // Log de salida con tiempo de ejecución
        if (log.isDebugEnabled()) {
            log.debug("<< Exit: {}. Execution time = {}ms. Result = {}", methodName,
                String.format(Locale.US, "%.3f", executionTimeMs), result);
        }

        // Escribir métricas al CSV (solo para queries importantes)
        if (isImportantQuery(methodName)) {
            writeToCSV(executionId, timestamp, className, methodName, executionTimeMs,
                      hasException, exceptionType, operationType);
        }

        return result;
    }

    /**
     * Determina si una query es importante para el análisis de rendimiento
     */
    private boolean isImportantQuery(String methodName) {
        return methodName.contains("Gfvt") ||           // Todas las tablas GFVT
               methodName.contains("Gcct") ||           // Tablas GCCT
               methodName.contains("select") ||         // Métodos select
               methodName.contains("cursor") ||         // Operaciones de cursor
               methodName.contains("Leer") ||           // Métodos de lectura
               methodName.contains("Inicio") ||         // Métodos de inicio
               methodName.contains("Cerrar") ||         // Métodos de cierre
               methodName.contains("Repository") ||     // Repositorios JPA
               methodName.contains("Port");             // Puertos de datos
    }

    /**
     * Determina el tipo de operación basado en el nombre del método
     */
    private String determineOperationType(String methodName) {
        if (methodName.contains("p7950") && methodName.contains("Gfvcdrc0")) {
            return "GFVTDRC0_CURSOR";
        } else if (methodName.contains("p8100") && methodName.contains("Gfvtdrc0")) {
            return "GFVTDRC0_SELECT";
        } else if (methodName.contains("p7100") && methodName.contains("Gfvtpra0")) {
            return "GFVTPRA0_SELECT";
        } else if (methodName.contains("p7200") && methodName.contains("Gfvtdma0")) {
            return "GFVTDMA0_CURSOR";
        } else if (methodName.contains("Gfvtdrc0")) {
            return "GFVTDRC0_OTHER";
        } else if (methodName.contains("Gfvtpra0")) {
            return "GFVTPRA0_OTHER";
        } else if (methodName.contains("Gfvtdma0")) {
            return "GFVTDMA0_OTHER";
        } else if (methodName.contains("Gfvtagt0")) {
            return "GFVTAGT0_CURSOR";
        } else if (methodName.contains("Gfvtcna0")) {
            return "GFVTCNA0_CURSOR";
        } else if (methodName.contains("Gfvtfla0")) {
            return "GFVTFLA0_CURSOR";
        } else if (methodName.contains("Gcctorv0")) {
            return "GCCTORV0_SELECT";
        } else if (methodName.contains("Repository") || methodName.contains("Port")) {
            return "JPA_REPOSITORY";
        } else if (methodName.contains("cursor") || methodName.contains("Cursor")) {
            return "CURSOR_OPERATION";
        } else if (methodName.contains("select") || methodName.contains("Select")) {
            return "SELECT_OPERATION";
        } else {
            return "OTHER";
        }
    }

    /**
     * Escribe métricas al archivo CSV
     */
    private synchronized void writeToCSV(long executionId, LocalDateTime timestamp, String className,
                                       String methodName, double executionTimeMs, boolean hasException,
                                       String exceptionType, String operationType) {
        try {
            // Escribir header si es la primera vez
            if (!csvHeaderWritten) {
                try (FileWriter writer = new FileWriter(CSV_FILE, false)) {
                    writer.write("ExecutionId,Timestamp,ClassName,MethodName,ExecutionTimeMs,HasException,ExceptionType,OperationType,Category\n");
                    csvHeaderWritten = true;
                }
            }

            // Escribir datos
            try (FileWriter writer = new FileWriter(CSV_FILE, true)) {
                String category = categorizeQuery(operationType);
                writer.write(String.format(Locale.US, "%d,%s,%s,%s,%.3f,%s,%s,%s,%s\n",
                    executionId,
                    timestamp.format(TIMESTAMP_FORMAT),
                    className,
                    methodName,
                    executionTimeMs,
                    hasException,
                    exceptionType,
                    operationType,
                    category
                ));
            }
        } catch (IOException e) {
            log.warn("Error writing to CSV file: {}", e.getMessage());
        }
    }

    /**
     * Categoriza las queries para análisis
     */
    private String categorizeQuery(String operationType) {
        if (operationType.contains("GFVTDRC0")) {
            return "OPTIMIZED_CACHE";
        } else if (operationType.contains("GFVTPRA0")) {
            return "OPTIMIZED_CACHE";
        } else if (operationType.contains("GFVTDMA0")) {
            return "OPTIMIZED_CACHE";
        } else if (operationType.contains("CURSOR")) {
            return "CURSOR_OPERATION";
        } else if (operationType.contains("SELECT") || operationType.contains("JPA")) {
            return "DATABASE_QUERY";
        } else {
            return "BUSINESS_LOGIC";
        }
    }
}