package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Locale;

@Aspect
@Component
public class MethodExecutionLoggingAspect {

    private static final Logger log = LoggerFactory.getLogger(MethodExecutionLoggingAspect.class);

    @Pointcut("(execution(* mx.com.gnp.cuentacorriente..*.*(..)) || execution(* com.base100.caravel.support..*.*(..))) && !within(mx.com.gnp.cuentacorriente.infraestructura.config.aop..*)")
    public void applicationPackagePointcut() {
        // Pointcut definition for application packages
    }

    @Around("applicationPackagePointcut()")
    public Object logMethodExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();

        if (!log.isDebugEnabled()) {
            return joinPoint.proceed();
        }

        Object[] methodArgs = joinPoint.getArgs();
        log.debug(">> Enter: {} with args = {}", methodName, Arrays.toString(methodArgs));

        // Captura del tiempo de inicio con mayor precisión
        long startTime = System.nanoTime();
        Object result;

        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            long executionTimeNs = System.nanoTime() - startTime;
            double executionTimeMs = executionTimeNs / 1_000_000.0;

            log.error("!! Exception in {} after {}ms: {} with cause: {}", methodName, String.format(Locale.US, "%.2f", executionTimeMs),
                    throwable.getClass().getSimpleName(), throwable.getMessage());
            throw throwable;
        }

        // Cálculo del tiempo de ejecución
        long executionTimeNs = System.nanoTime() - startTime;
        double executionTimeMs = executionTimeNs / 1_000_000.0;

        // Log de salida con tiempo de ejecución
        log.debug("<< Exit: {}. Execution time = {}ms. Result = {}", methodName, String.format(Locale.US, "%.2f", executionTimeMs), result);

        return result;
    }
}