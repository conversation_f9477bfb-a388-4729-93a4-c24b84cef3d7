package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Aspect
@Component
public class MethodExecutionLoggingAspect {

    private static final Logger log = LoggerFactory.getLogger(MethodExecutionLoggingAspect.class);

    @Pointcut("(execution(* mx.com.gnp.cuentacorriente..*.*(..)) || execution(* com.base100.caravel.support..*.*(..))) && !within(mx.com.gnp.cuentacorriente.infraestructura.config.aop..*)")
    public void applicationPackagePointcut() {
        // Pointcut definition for application packages
    }

    @Around("applicationPackagePointcut()")
    public Object logMethodExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();

        if (!log.isDebugEnabled()) {
            return joinPoint.proceed();
        }

        Object[] methodArgs = joinPoint.getArgs();
        log.debug(">> Enter: {} with args = {}", methodName, Arrays.toString(methodArgs));

        // Captura del tiempo de inicio con mayor precisión
        long startTime = System.nanoTime();
        Object result;

        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            long executionTimeNs = System.nanoTime() - startTime;
            double executionTimeMs = executionTimeNs / 1_000_000.0;

            log.error("!! Exception in {} after {:.2f}ms: {} with cause: {}", methodName, executionTimeMs,
                    throwable.getClass().getSimpleName(), throwable.getMessage());
            throw throwable;
        }

        // Cálculo del tiempo de ejecución
        long executionTimeNs = System.nanoTime() - startTime;
        double executionTimeMs = executionTimeNs / 1_000_000.0;

        // Log de salida con tiempo de ejecución
        log.debug("<< Exit: {}. Execution time = {:.2f}ms. Result = {}", methodName, executionTimeMs, result);

        return result;
    }

    private void logMethodExit(String methodName, double executionTimeMs, Object result) {
        String timeFormatted = formatExecutionTime(executionTimeMs);

        if (showResult) {
            log.debug("🟢 EXIT: {} {} - Result: {}", methodName, timeFormatted, formatResult(result));
        } else {
            log.debug("🟢 EXIT: {} {}", methodName, timeFormatted);
        }

        // Alerta de rendimiento si excede el umbral
        if (executionTimeMs > performanceThresholdMs) {
            log.warn("⚠️  PERFORMANCE: {} took {:.2f}ms (threshold: {}ms)",
                methodName, executionTimeMs, performanceThresholdMs);
        }
    }

    private String formatExecutionTime(double executionTimeMs) {
        if (executionTimeMs < 1.0) {
            return String.format("(%.3fms)", executionTimeMs);
        } else if (executionTimeMs < 1000.0) {
            return String.format("(%.2fms)", executionTimeMs);
        } else {
            double seconds = executionTimeMs / 1000.0;
            return String.format("(%.2fs)", seconds);
        }
    }


    private String formatArguments(Object[] args) {
        if (args == null || args.length == 0) {
            return "[]";
        }

        // Limitar el tamaño de los argumentos para evitar logs muy largos
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < args.length; i++) {
            if (i > 0) sb.append(", ");
            sb.append(formatSingleArgument(args[i]));
        }
        sb.append("]");
        return sb.toString();
    }

    private String formatSingleArgument(Object arg) {
        if (arg == null) {
            return "null";
        }

        String argStr = arg.toString();
        // Limitar el tamaño del argumento para evitar logs excesivamente largos
        if (argStr.length() > 100) {
            return argStr.substring(0, 97) + "...";
        }
        return argStr;
    }

    private String formatResult(Object result) {
        if (result == null) {
            return "null";
        }

        String resultStr = result.toString();
        // Limitar el tamaño del resultado
        if (resultStr.length() > 200) {
            return resultStr.substring(0, 197) + "...";
        }
        return resultStr;
    }
}