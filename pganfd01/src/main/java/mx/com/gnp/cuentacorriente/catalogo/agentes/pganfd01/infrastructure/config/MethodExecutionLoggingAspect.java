package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Aspect
@Component
public class MethodExecutionLoggingAspect {

    private static final Logger log = LoggerFactory.getLogger(MethodExecutionLoggingAspect.class);

    @Pointcut("(execution(* mx.com.gnp.cuentacorriente..*.*(..)) || execution(* com.base100.caravel.support..*.*(..))) && !within(mx.com.gnp.cuentacorriente.infraestructura.config.aop..*)")
    public void applicationPackagePointcut() {
        // Pointcut definition remains the same.
    }

    @Around("applicationPackagePointcut()")
    public Object logMethodExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();

        if (!log.isDebugEnabled()) {
            return joinPoint.proceed();
        }

        Object[] methodArgs = joinPoint.getArgs();
        log.debug(">> Enter: {} with args = {}", methodName, Arrays.toString(methodArgs));

        // 1. Captura del tiempo de inicio
        long startTime = System.currentTimeMillis(); // <-- AQUÍ
        Object result;

        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            log.error("!! Exception in {}: {} with cause: {}", methodName, throwable.getClass().getSimpleName(),
                    throwable.getMessage());
            throw throwable;
        }

        // 2. Cálculo del tiempo de ejecución
        long executionTime = System.currentTimeMillis() - startTime; // <-- AQUÍ

        // 3. Inclusión del tiempo en el mensaje de log de salida
        log.debug("<< Exit: {}. Execution time = {}ms. Result = {}", methodName, executionTime, result); // <-- AQUÍ

        return result;
    }
}