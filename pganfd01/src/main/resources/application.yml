application:
  title: Program pganfd01
  description: |
    ### pganfd01 in "Hexagonal Architecture"
  version: 1.0.0.SNAPSHOT

spring:
  main:
    web-application-type: NONE
  datasource:
    url: jdbc:db2://************:25010/GNPDB296:currentSchema=NPMPR;
    user: db2inst1
    password: gnppassword$1
context:
  sort-program-name: /opt/app/ttsortx64
os390:
  base-path: /nfs
  data-path: ${os390.base-path}/data

caravel:
  support:
    bucket:
      directory: ${os390.data-path}
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - 'PGA.EAFV0101'
          - 'SYSBKW.EAFV0401(+1)'
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:

# Configuración del Aspect de logging
logging:
  aspect:
    enabled: true                    # Habilitar/deshabilitar el aspect de logging
    show-args: false                 # Mostrar argumentos de métodos (puede contener información sensible)
    show-result: false               # Mostrar resultado de métodos
    performance-threshold: 1000      # Umbral en ms para alertas de rendimiento
  level:
    mx.com.gnp.cuentacorriente: DEBUG  # Necesario para que funcione el aspect
