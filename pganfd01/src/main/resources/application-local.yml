spring:
  datasource:
    #url: *******************************************************;
    url: ************************************************************;
    #url: *******************************************************;
    user: db2inst1
    password: db2inst1
    #password: gnppassword$1
  batch:
    jdbc:
      initialize-schema: always
  jpa:
    properties:
      hibernate:
        format_sql: true
context:
  sort-program-name: ../ttsort/run_ttsort.sh
os390:
  base-path: /tmp/gnp/cuentacorriente
caravel:
  support:
    bucket:
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"

logging:
  aspect:
    enabled: true                    # Habilitar/deshabilitar el aspect de logging
    show-args: true                  # En local podemos mostrar argumentos para debugging
    show-result: true                # En local podemos mostrar resultados para debugging
    performance-threshold: 500       # Umbral más bajo en local para detectar problemas
  level:
    mx.com.gnp.cuentacorriente: DEBUG  # Necesario para que funcione el aspect
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

