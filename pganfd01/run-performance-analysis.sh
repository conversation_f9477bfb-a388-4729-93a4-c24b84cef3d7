#!/bin/bash

# Script para ejecutar análisis de rendimiento de PGANFD01
# Autor: Optimización de Rendimiento JPA
# Fecha: $(date)

echo "🚀 ANÁLISIS DE RENDIMIENTO - PGANFD01"
echo "====================================="

# Configuración
CSV_FILE="pganfd01/logs/query-performance-metrics.csv"
LOG_FILE="pganfd01/logs/application.log"
ANALYSIS_SCRIPT="pganfd01/analyze-performance.py"

# Verificar que existe el CSV
if [ ! -f "$CSV_FILE" ]; then
    echo "❌ No se encontró el archivo CSV: $CSV_FILE"
    echo ""
    echo "💡 Para generar el CSV:"
    echo "   1. Ejecuta el programa PGANFD01"
    echo "   2. Asegúrate que logging.level esté en DEBUG"
    echo "   3. El CSV se generará automáticamente"
    echo ""
    exit 1
fi

# Mostrar información del archivo
echo "📁 Archivo CSV: $CSV_FILE"
echo "📊 Tamaño: $(du -h "$CSV_FILE" | cut -f1)"
echo "📈 Registros: $(wc -l < "$CSV_FILE") líneas"
echo ""

# Verificar Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 no está instalado"
    exit 1
fi

# Verificar pandas
if ! python3 -c "import pandas" 2>/dev/null; then
    echo "⚠️  Pandas no está instalado. Instalando..."
    pip3 install pandas
fi

# Ejecutar análisis
echo "🔍 Ejecutando análisis de rendimiento..."
echo ""

python3 "$ANALYSIS_SCRIPT" "$CSV_FILE"

echo ""
echo "✅ Análisis completado!"
echo ""
echo "📋 ARCHIVOS GENERADOS:"
echo "   📊 CSV de métricas: $CSV_FILE"
echo "   📈 Gráficos: pganfd01/logs/performance-analysis.png"
echo "   📝 Log completo: $LOG_FILE"
echo ""
echo "💡 COMANDOS ÚTILES:"
echo "   # Ver últimas 100 líneas del CSV"
echo "   tail -100 $CSV_FILE"
echo ""
echo "   # Filtrar solo queries lentas (>100ms)"
echo "   awk -F',' '\$5 > 100 {print}' $CSV_FILE"
echo ""
echo "   # Contar queries por tipo"
echo "   cut -d',' -f8 $CSV_FILE | sort | uniq -c | sort -nr"
echo ""
echo "   # Ver queries con excepciones"
echo "   grep 'true' $CSV_FILE"
