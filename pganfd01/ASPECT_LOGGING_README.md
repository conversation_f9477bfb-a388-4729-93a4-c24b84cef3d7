# Aspect de Logging de Métodos - Configuración y Uso

## Descripción

El `MethodExecutionLoggingAspect` es un aspecto de Spring AOP que proporciona logging automático para todos los métodos de la aplicación, incluyendo:

- ✅ Tiempo de entrada y salida de métodos
- ⏱️ Medición precisa del tiempo de ejecución (usando `System.nanoTime()`)
- 🚨 Alertas de rendimiento para métodos lentos
- 🔍 Logging de excepciones con tiempo de ejecución
- 🎛️ Configuración flexible mediante propiedades

## Características Principales

### 1. Logging de Entrada y Salida
```
🔵 ENTER: ClassName.methodName()
🟢 EXIT: ClassName.methodName() (15.23ms)
```

### 2. Medición de Tiempo Precisa
- Utiliza `System.nanoTime()` para mayor precisión
- Formato automático: ms para tiempos cortos, segundos para tiempos largos
- Ejemplo: `(0.123ms)`, `(15.67ms)`, `(2.34s)`

### 3. Alertas de Rendimiento
```
⚠️  PERFORMANCE: ClassName.methodName() took 1500.00ms (threshold: 1000ms)
```

### 4. Logging de Excepciones
```
❌ EXCEPTION in ClassName.methodName() after 45.67ms: RuntimeException - Error message
```

## Configuración

### Propiedades Disponibles

```yaml
logging:
  aspect:
    enabled: true                    # Habilitar/deshabilitar el aspect
    show-args: false                 # Mostrar argumentos de métodos
    show-result: false               # Mostrar resultado de métodos
    performance-threshold: 1000      # Umbral en ms para alertas de rendimiento
  level:
    mx.com.gnp.cuentacorriente: DEBUG  # Necesario para que funcione el aspect
```

### Configuración por Ambiente

#### Producción (`application.yml`)
```yaml
logging:
  aspect:
    enabled: true
    show-args: false      # No mostrar argumentos por seguridad
    show-result: false    # No mostrar resultados por seguridad
    performance-threshold: 1000
```

#### Desarrollo (`application-local.yml`)
```yaml
logging:
  aspect:
    enabled: true
    show-args: true       # Mostrar argumentos para debugging
    show-result: true     # Mostrar resultados para debugging
    performance-threshold: 500  # Umbral más bajo para detectar problemas
```

## Pointcut Configuration

El aspecto se aplica a:
- ✅ Todos los paquetes `mx.com.gnp.cuentacorriente.*`
- ✅ Todos los paquetes `com.base100.caravel.support.*`
- ❌ Excluye paquetes `mx.com.gnp.cuentacorriente.infraestructura.config.aop.*`

## Ejemplos de Salida

### Método Normal
```
🔵 ENTER: UserService.findById()
🟢 EXIT: UserService.findById() (12.45ms)
```

### Método con Argumentos (show-args: true)
```
🔵 ENTER: UserService.findById() with args = [123]
🟢 EXIT: UserService.findById() (12.45ms) - Result: User{id=123, name='John'}
```

### Método Lento
```
🔵 ENTER: ReportService.generateReport()
🟢 EXIT: ReportService.generateReport() (1.25s)
⚠️  PERFORMANCE: ReportService.generateReport() took 1250.00ms (threshold: 1000ms)
```

### Método con Excepción
```
🔵 ENTER: PaymentService.processPayment()
❌ EXCEPTION in PaymentService.processPayment() after 156.78ms: ValidationException - Invalid payment amount
```

## Consideraciones de Seguridad

### Argumentos Sensibles
- Por defecto, `show-args: false` para evitar logging de información sensible
- En desarrollo local se puede habilitar para debugging
- Los argumentos se truncan automáticamente si son muy largos (>100 caracteres)

### Resultados Sensibles
- Por defecto, `show-result: false` para evitar logging de información sensible
- Los resultados se truncan automáticamente si son muy largos (>200 caracteres)

## Configuración de Spring Boot

### Habilitación de AOP
Asegúrate de que tu aplicación tenga `@EnableAspectJAutoProxy`:

```java
@SpringBootApplication
@EnableAspectJAutoProxy
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### Dependencias Necesarias
```gradle
implementation 'org.springframework.boot:spring-boot-starter-aop'
```

## Troubleshooting

### El Aspect No Funciona
1. ✅ Verificar que `@EnableAspectJAutoProxy` esté presente
2. ✅ Verificar que el nivel de logging sea `DEBUG` para el paquete
3. ✅ Verificar que `logging.aspect.enabled: true`
4. ✅ Verificar que las dependencias de AOP estén incluidas

### Demasiados Logs
1. 🎛️ Cambiar `logging.aspect.enabled: false`
2. 🎛️ Aumentar `performance-threshold` para reducir alertas
3. 🎛️ Cambiar nivel de logging a `INFO` o superior

### Información Sensible en Logs
1. 🔒 Asegurar `show-args: false`
2. 🔒 Asegurar `show-result: false`
3. 🔒 Revisar configuración por ambiente

## Rendimiento

- ✅ Impacto mínimo cuando logging está deshabilitado
- ✅ Verificación temprana de nivel de logging
- ✅ Formateo condicional de argumentos y resultados
- ✅ Uso de `System.nanoTime()` para medición precisa
