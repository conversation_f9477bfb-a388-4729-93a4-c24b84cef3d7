Compilado para funcionar en docker

Nota. Al estar compilado en estático, se ha comprobado empíricamente funciona correctamente en:
- Docker
- Debian 12
- Red Hat Enterprise Linux 8.8
- Red Hat Enterprise Linux 8.10
- WSL (Subsistema de Windows para Linux)
 
 La versión 0.97 implementa:
 - Correccion en INCLUDE/OMIT tanto generico como en OUTFIL. Comparaba los CHAR en Ascii, en lugar de EBCDIC. Ahora, por defecto compara en EBCDIC 037.
 - Si se desea comparar utilizando otros CCSID, se ha implementado el parametro "-includeccsid <id>", siendo id: 0, 037 o 284
     0 -> Comparacion ASCII
	 037 -> Comparacion usando CCSID 037 (ingles) -> Si no se indica este parámetro, este es el valor por defecto.
	 284 -> Comparacion usando CCSID 284 (spanish)
 