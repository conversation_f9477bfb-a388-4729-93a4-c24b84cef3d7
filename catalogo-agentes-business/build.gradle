plugins {
    id 'java-library'
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.mapstruct:mapstruct:1.6.1'

    implementation "mx.com.gnp.cuentacorriente:common-business-spring-boot-starter:${gnpCuentaCorrienteCommonVersion}"
    implementation "mx.com.gnp.cuentacorriente:common-business:${gnpCuentaCorrienteCommonVersion}"
	
    implementation 'com.hazelcast:hazelcast:5.3.7'
    implementation 'com.hazelcast:hazelcast-spring:5.3.7'
    implementation("com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.16.1")
    implementation("org.apache.cxf:cxf-spring-boot-starter-jaxrs:4.0.2")
    implementation("org.apache.cxf:cxf-rt-rs-security-cors:4.0.2")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3")
    implementation("net.sf.jt400:jt400:20.0.6")
    implementation 'com.google.cloud:google-cloud-storage:2.53.0'

    implementation fileTree(dir: '../caravel-support', include: '*.jar')
}

apply from: "${rootDir}/publish.java.gradle"
