
/**
 *  Program: Ggnf0100.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.RegMaestro;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsDatosAbend;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsCodigos;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagt0cpy.Dclgfvtagt0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcna0cpy.Dclgfvtcna0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtfla0cpy.Dclgfvtfla0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtpra0cpy.Dclgfvtpra0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdma0cpy.Gfvtdma0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdmz0cpy.Dclgfvtdmz0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtmte0cpy.Dclgfvtmte0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctmnd0cpy.Dclgcctmnd0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcma0cpy.Dclgfvtcma0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctpob0cpy.Dclgcctpob0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctedo0cpy.Dclgcctedo0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctorv0cpy.Dclgcctorv0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdrc0cpy.Dclgfvtdrc0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtafo1cpy.Gfvtafo0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtagt0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtcna0Null00;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtcna0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtfla0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtfla1Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtfla2Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGcctorv0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGcctpob0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtpra0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtmnd0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtedo0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsGfvtdrc0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsContadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsIndices;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsIndicadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsRegistros;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsCampos;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.Db2AaaaMmDd;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.WsFecAammddr;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtpra0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtmte0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctedo0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctorv0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtafo0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcna0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdrc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdmz0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctpob0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctmnd0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtfla0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtpra0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtmte0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctedo0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctorv0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtafo0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcna0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdrc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcma0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdmz0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctpob0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctmnd0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtfla0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdma0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggnf0100 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private RegMaestro regMaestro = new  RegMaestro();                       // 01 REG-MAESTRO UsedAsReferenceParameter
//  private String wsWork = "WORKING";                                      // 01 WS-WORK X(20) // [@WARNING variable NOT USED]
    private WsDatosAbend wsDatosAbend = new  WsDatosAbend();                 // 01 WS-DATOS-ABEND
    private int db2ReturnCode;                                              // 01 DB2-RETURN-CODE S9(9) COMPUTATIONAL
    // Level 88 - Conditional names
    private static final int DB2_OK = 0;
    private static final int DB2_NOTFND = 100;
    private static final int DB2_DUPREC = 803;
    private static final int DB2_NOTUNI = -811;
    private static final int DB2_DATETIME = -181;
    private static final int DB2_SYSTEM = -911;

    private WsCodigos wsCodigos = new  WsCodigos();                          // 01 WS-CODIGOS

    // Copy file QA$CBLCPY$GFVTAGT0
    private Dclgfvtagt0 dclgfvtagt0 = new  Dclgfvtagt0();                    // 01 DCLGFVTAGT0
    // End copy file QA$CBLCPY$GFVTAGT0

    // Copy file QA$CBLCPY$GFVTCNA0
    private Dclgfvtcna0 dclgfvtcna0 = new  Dclgfvtcna0();                    // 01 DCLGFVTCNA0 UsedAsParameter
    // End copy file QA$CBLCPY$GFVTCNA0

    // Copy file QA$CBLCPY$GFVTFLA0
    private Dclgfvtfla0 dclgfvtfla0 = new  Dclgfvtfla0();                    // 01 DCLGFVTFLA0 UsedAsParameter
    // End copy file QA$CBLCPY$GFVTFLA0

    // Copy file QA$CBLCPY$GFVTPRA0
    private Dclgfvtpra0 dclgfvtpra0 = new  Dclgfvtpra0();                    // 01 DCLGFVTPRA0
    // End copy file QA$CBLCPY$GFVTPRA0

    // Copy file QA$CBLCPY$GFVTDMA0
    private Gfvtdma0 gfvtdma0 = new  Gfvtdma0();                             // 01 GFVTDMA0
    // End copy file QA$CBLCPY$GFVTDMA0

    // Copy file QA$CBLCPY$GFVTDMZ0
    private Dclgfvtdmz0 dclgfvtdmz0 = new  Dclgfvtdmz0();                    // 01 DCLGFVTDMZ0
    // End copy file QA$CBLCPY$GFVTDMZ0

    // Copy file QA$CBLCPY$GFVTMTE0
    private Dclgfvtmte0 dclgfvtmte0 = new  Dclgfvtmte0();                    // 01 DCLGFVTMTE0
    // End copy file QA$CBLCPY$GFVTMTE0

    // Copy file QA$CBLCPY$GCCTMND0
    private Dclgcctmnd0 dclgcctmnd0 = new  Dclgcctmnd0();                    // 01 DCLGCCTMND0
    // End copy file QA$CBLCPY$GCCTMND0

    // Copy file QA$CBLCPY$GFVTCMA0
    private Dclgfvtcma0 dclgfvtcma0 = new  Dclgfvtcma0();                    // 01 DCLGFVTCMA0
    // End copy file QA$CBLCPY$GFVTCMA0

    // Copy file QA$CBLCPY$GCCTPOB0
    private Dclgcctpob0 dclgcctpob0 = new  Dclgcctpob0();                    // 01 DCLGCCTPOB0
    // End copy file QA$CBLCPY$GCCTPOB0

    // Copy file QA$CBLCPY$GCCTEDO0
    private Dclgcctedo0 dclgcctedo0 = new  Dclgcctedo0();                    // 01 DCLGCCTEDO0
    // End copy file QA$CBLCPY$GCCTEDO0

    // Copy file QA$CBLCPY$GCCTORV0
    private Dclgcctorv0 dclgcctorv0 = new  Dclgcctorv0();                    // 01 DCLGCCTORV0
    // End copy file QA$CBLCPY$GCCTORV0

    // Copy file QA$CBLCPY$GFVTDRC0
    private Dclgfvtdrc0 dclgfvtdrc0 = new  Dclgfvtdrc0();                    // 01 DCLGFVTDRC0
    // End copy file QA$CBLCPY$GFVTDRC0

    // Copy file QA$CBLCPY$GFVTAFO1
    private Gfvtafo0 gfvtafo0 = new  Gfvtafo0();                             // 01 GFVTAFO0
    // End copy file QA$CBLCPY$GFVTAFO1

    // Level 1
    private WsGfvtagt0Null wsGfvtagt0Null = new  WsGfvtagt0Null();           // 01 WS-GFVTAGT0-NULL
    private WsGfvtcna0Null00 wsGfvtcna0Null00 = new  WsGfvtcna0Null00();     // 01 WS-GFVTCNA0-NULL00 UsedAsParameter
    private WsGfvtcna0Null wsGfvtcna0Null = new  WsGfvtcna0Null();           // 01 WS-GFVTCNA0-NULL UsedAsParameter
    private WsGfvtfla0Null wsGfvtfla0Null = new  WsGfvtfla0Null();           // 01 WS-GFVTFLA0-NULL
    private WsGfvtfla1Null wsGfvtfla1Null = new  WsGfvtfla1Null();           // 01 WS-GFVTFLA1-NULL
    private WsGfvtfla2Null wsGfvtfla2Null = new  WsGfvtfla2Null();           // 01 WS-GFVTFLA2-NULL
    private WsGcctorv0Null wsGcctorv0Null = new  WsGcctorv0Null();           // 01 WS-GCCTORV0-NULL
    private WsGcctpob0Null wsGcctpob0Null = new  WsGcctpob0Null();           // 01 WS-GCCTPOB0-NULL
    private WsGfvtpra0Null wsGfvtpra0Null = new  WsGfvtpra0Null();           // 01 WS-GFVTPRA0-NULL
//  private WsGfvtmte0Null wsGfvtmte0Null = new  WsGfvtmte0Null();               // 01 WS-GFVTMTE0-NULL // [@WARNING variable NOT USED]
    private WsGfvtmnd0Null wsGfvtmnd0Null = new  WsGfvtmnd0Null();           // 01 WS-GFVTMND0-NULL
    private WsGfvtedo0Null wsGfvtedo0Null = new  WsGfvtedo0Null();           // 01 WS-GFVTEDO0-NULL
    private WsGfvtdrc0Null wsGfvtdrc0Null = new  WsGfvtdrc0Null();           // 01 WS-GFVTDRC0-NULL
    private WsContadores wsContadores = new  WsContadores();                 // 01 WS-CONTADORES
    private WsIndices wsIndices = new  WsIndices();                          // 01 WS-INDICES
    private WsIndicadores wsIndicadores = new  WsIndicadores();              // 01 WS-INDICADORES
    private WsRegistros wsRegistros = new  WsRegistros();                    // 01 WS-REGISTROS
    private WsCampos wsCampos = new  WsCampos();                             // 01 WS-CAMPOS
//  private WsCurrent wsCurrent = new  WsCurrent();                              // 01 WS-CURRENT // [@WARNING variable NOT USED]
    private Db2AaaaMmDd db2AaaaMmDd = new  Db2AaaaMmDd();                    // 01 DB2-AAAA-MM-DD
//  private WsFecAaaa wsFecAaaa = new  WsFecAaaa();                              // 01 WS-FEC-AAAA // [@WARNING variable NOT USED]
    private WsFecAammddr wsFecAammddr = new  WsFecAammddr();                 // 01 WS-FEC-AAMMDDR REDEFINED BY WS-FEC-AAMMDD
    private UnsignedNumericVar wsFecAammdd = new UnsignedNumericVar(null, this.wsFecAammddr, 6, 0);          // 01 WS-FEC-AAMMDD REDEFINES WS-FEC-AAMMDDR 9(6)

    private Sqlca sqlca;
    // Files
    private ISequentialFile maestro;



    // Declare Sql Ports
    private IGfvtpra0Port gfvtpra0Port;
    private IGfvtmte0Port gfvtmte0Port;
    private IGcctedo0Port gcctedo0Port;
    private IGcctorv0Port gcctorv0Port;
    private IGfvtafo0Port gfvtafo0Port;
    private IGfvtcna0Port gfvtcna0Port;
    private IGfvtdrc0Port gfvtdrc0Port;
    private IGfvtcma0Port gfvtcma0Port;
    private IGfvtdmz0Port gfvtdmz0Port;
    private IGcctpob0Port gcctpob0Port;
    private IGcctmnd0Port gcctmnd0Port;
    private IGfvtagt0Port gfvtagt0Port;
    private IGfvtfla0Port gfvtfla0Port;
    private IGfvtdma0Port gfvtdma0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtpra0Model> gfvtpra0Model;
    private Optional<Gfvtmte0Model> gfvtmte0Model;
    private Optional<Gcctedo0Model> gcctedo0Model;
    private Optional<Gcctorv0Model> gcctorv0Model;
    private Optional<Gfvtafo0Model> gfvtafo0Model;
    private Optional<Gfvtcna0Model> gfvtcna0Model;
    private Optional<Gfvtdrc0Model> gfvtdrc0Model;
    private Optional<Gfvtcma0Model> gfvtcma0Model;
    private Optional<Gfvtdmz0Model> gfvtdmz0Model;
    private Optional<Gcctpob0Model> gcctpob0Model;
    private Optional<Gcctmnd0Model> gcctmnd0Model;
    private Optional<Gfvtagt0Model> gfvtagt0Model;
    private Optional<Gfvtfla0Model> gfvtfla0Model;
    private Optional<Gfvtdma0Model> gfvtdma0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtagt0Model> gfvcagt0Cursor;
    private ICursor<Gfvtcna0Model> gfvccna0Cursor;
    private ICursor<Gfvtcna0Model> gfvccna1Cursor;
    private ICursor<Gfvtfla0Model> gfvcfla0Cursor;
    private ICursor<Gfvtfla0Model> gfvcfla1Cursor;
    private ICursor<Gfvtfla0Model> gfvcfla3Cursor;
    private ICursor<Gfvtfla0Model> gfvcfla2Cursor;
    private ICursor<Gfvtdma0Model> gfvcdma0Cursor;
    private ICursor<Gfvtdrc0Model> gfvcdrc0Cursor;
    
    // Variables para optimización y métricas
    private int drcResultForContract; // Cache del resultado de GFVTDRC0 por contrato
    private long startTime; // Tiempo de inicio
    private int totalGfvtdrc0Calls; // Total de llamadas originales a p8100LeerGfvtdrc0
    private int totalGfvtdrc0CallsOptimized; // Total de llamadas optimizadas

    // Cache inteligente para evitar consultas duplicadas
    private Map<String, Integer> drcCache = new HashMap<>(); // Cache de resultados GFVTDRC0

    public Ggnf0100(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initFiles();
    }

    public void initFiles() {

        this.maestro = createSequentialFile("MAESTRO", "MTRO", regMaestro);


    }

    public void run() {
        sqlDelayedParagraph();
        start();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(gfvcagt0Cursor == null) {
            gfvcagt0Cursor = gfvtagt0Port.createCursorGfvcagt0(
                () -> Gfvtagt0Model.builder()
                    .agtIdr(wsCampos.getWsAgtIdr())
                .build());
        }
        if(gfvccna0Cursor == null) {
            gfvccna0Cursor = gfvtcna0Port.createCursorGfvccna0(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                .build());
        }
        if(gfvccna1Cursor == null) {
            gfvccna1Cursor = gfvtcna0Port.createCursorGfvccna1(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(dclgcctorv0.getOrvAgtIdrGte().toInt())
                    .cnaGerZonEnv(dclgcctorv0.getOrvCve().toInt())
                .build());
        }
        if(gfvcfla0Cursor == null) {
            gfvcfla0Cursor = gfvtfla0Port.createCursorGfvcfla0(
                () -> Gfvtfla0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                .build());
        }
        if(gfvcfla1Cursor == null) {
            gfvcfla1Cursor = gfvtfla0Port.createCursorGfvcfla1(
                () -> Gfvtfla0Model.builder()
                    .agtIdr(dclgfvtcna0.getAgtIdrRcl().toInt())
                    .mteCve(dclgfvtcna0.getMteCve().toInt())
                .build());
        }
        if(gfvcfla3Cursor == null) {
            gfvcfla3Cursor = gfvtfla0Port.createCursorGfvcfla1_2(
                () -> Gfvtfla0Model.builder()
                    .agtIdr(dclgfvtcna0.getAgtIdrRcl().toInt())
                .build());
        }
        if(gfvcfla2Cursor == null) {
            gfvcfla2Cursor = gfvtfla0Port.createCursorGfvcfla2(
                () -> Gfvtfla0Model.builder()
                    .agtIdr(dclgcctorv0.getOrvAgtIdrGte().toInt())
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                .build());
        }
        if(gfvcdma0Cursor == null) {
            gfvcdma0Cursor = gfvtdma0Port.createCursorGfvcdma0(
                () -> Gfvtdma0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .tdmCve(wsCampos.getWsTdmCve())
                .build());
        }
        if(gfvcdrc0Cursor == null) {
            gfvcdrc0Cursor = gfvtdrc0Port.createCursorGfvcdrc0(
                () -> Gfvtdrc0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                .build());
        }
    }

    /**
     *  Paragraph: (START).
     */
    void start() {
        p1000Inicio();
        while (!(wsIndicadores.getWsFinAgt() == 1)) {
            p2000Proceso();
        }
        p4000Fin();
        throw new GobackException();
        /* [@WARNING Unreachable code]
        throw new StopRunException();
        */
    }

    /**
     *  Paragraph: 1000-INICIO.
     *
     * FUNCION INICIAL.
     */
    void p1000Inicio() {
        //                                                                  11730000
        //      ACCEPT  WS-PAR-FEC                                          11740000
        //      DISPLAY  '*************************************'.           11750000
        //      DISPLAY  '**   INICIA EL PROGRAMA GGNF0100   **'.           11760000
        //      DISPLAY  '*************************************'.           11770000
        //      DISPLAY  '*                                   *'.           11780000
        //      DISPLAY  '*    CON FECHA PARAMETRO  = HOY     *' WS-PAR-FEC 11790000
        //      DISPLAY  '*                                   *'.           11800000
        //      DISPLAY  '*************************************'.           11810000
        //                                                                  11820000
        
        // Inicializar métricas de rendimiento
        startTime = System.currentTimeMillis();
        totalGfvtdrc0Calls = 0;
        totalGfvtdrc0CallsOptimized = 0;

        // Limpiar cache al inicio
        drcCache.clear();

        maestro.openOutput();
        wsCampos.setWsAgtIdr(0);

    }

    /**
     *  Paragraph: 2000-PROCESO.
     *
     * RUTINA QUE ACCESA TODOS LO AGENTES.
     * ABRE CURSOR GFVCAGT0.
     * ABRE CURSOR GFVCAGT0.
     */
    void p2000Proceso() {
        dclgfvtagt0.initialize();
        wsIndicadores.setWsFinAgt(0);

        //                                                                  11970000
        p2100InicioGfvtagt0();
        while (!(wsIndicadores.getWsFinAgt() == 1)) {
            p3000ProcesoAgentes();
        }
        p2100CerrarGfvtagt0();
    }

    /**
     *  Paragraph: 2100-INICIO-GFVTAGT0.
     *
     * ABRE CURSOR Y LEE EL PRIMER AGENTE
     */
    void p2100InicioGfvtagt0() {
        //                                                                  12100000
        wsDatosAbend.setWsProcesoLlama("2100-INICIO-GFVTAGT0          ");

        wsDatosAbend.setWsProcesoEjecuta("2100-INICIO-GFVTAGT0          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTAGT0        ");

        //                                                                  12150000
        //                                                                  12160000
        gfvcagt0Cursor.open();
        //                                                                  12200000
        db2check();
        //                                                                  12220000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
        //                                                                  12270000
        p2100LeerGfvtagt0();
    }

    /**
     *  Paragraph: 2100-LEER-GFVTAGT0.
     *
     * LEER  TABLA  GFVTAGT0
     */
    void p2100LeerGfvtagt0() {
        //                                                                  12370000
        wsDatosAbend.setWsProcesoLlama("2100-LEER-GFVTAGT0            ");

        wsDatosAbend.setWsProcesoEjecuta("2100-LEER-GFVTAGT0            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTAGT0        ");

        //                                                                  12420000
        dclgfvtagt0.initialize();
        gfvtagt0Model = gfvcagt0Cursor.next();

        if (gfvtagt0Model.isPresent()) {
            dclgfvtagt0.setAgtIdr(fromHostInt(gfvtagt0Model.get().agtIdr()));
            dclgfvtagt0.setAgtRfc(fromHostString(gfvtagt0Model.get().agtRfc()));
            wsGfvtagt0Null.setAgt02(isNull(gfvtagt0Model.get().agtRfc()) ? -1 : 0);
            dclgfvtagt0.setAgtNom(fromHostString(gfvtagt0Model.get().agtNom()));
            wsGfvtagt0Null.setAgt03(isNull(gfvtagt0Model.get().agtNom()) ? -1 : 0);
            dclgfvtagt0.setAgtApePat(fromHostString(gfvtagt0Model.get().agtApePat()));
            wsGfvtagt0Null.setAgt04(isNull(gfvtagt0Model.get().agtApePat()) ? -1 : 0);
            dclgfvtagt0.setAgtApeMat(fromHostString(gfvtagt0Model.get().agtApeMat()));
            wsGfvtagt0Null.setAgt05(isNull(gfvtagt0Model.get().agtApeMat()) ? -1 : 0);
            dclgfvtagt0.setAgtFecNac(fromHostString(gfvtagt0Model.get().agtFecNac()));
            wsGfvtagt0Null.setAgt06(isNull(gfvtagt0Model.get().agtFecNac()) ? -1 : 0);
            dclgfvtagt0.setAgtNumCed(fromHostString(gfvtagt0Model.get().agtNumCed()));
            wsGfvtagt0Null.setAgt07(isNull(gfvtagt0Model.get().agtNumCed()) ? -1 : 0);
            dclgfvtagt0.setAgtFecIniCed(fromHostString(gfvtagt0Model.get().agtFecIniCed()));
            wsGfvtagt0Null.setAgt08(isNull(gfvtagt0Model.get().agtFecIniCed()) ? -1 : 0);
            dclgfvtagt0.setAgtFecFinCed(fromHostString(gfvtagt0Model.get().agtFecFinCed()));
            wsGfvtagt0Null.setAgt09(isNull(gfvtagt0Model.get().agtFecFinCed()) ? -1 : 0);
            dclgfvtagt0.setAgtExpSeg(fromHostString(gfvtagt0Model.get().agtExpSeg()));
            dclgfvtagt0.setAgtTraEsp(fromHostString(gfvtagt0Model.get().agtTraEsp()));
            dclgfvtagt0.setAgtClfLmr(fromHostInt(gfvtagt0Model.get().agtClfLmr()));
            wsGfvtagt0Null.setAgt12(isNull(gfvtagt0Model.get().agtClfLmr()) ? -1 : 0);
            dclgfvtagt0.setFecUltAct(fromHostString(gfvtagt0Model.get().fecUltAct()));
            dclgfvtagt0.setAgtRazSoc(fromHostString(gfvtagt0Model.get().agtRazSoc()));
            wsGfvtagt0Null.setAgt14(isNull(gfvtagt0Model.get().agtRazSoc()) ? -1 : 0);
            dclgfvtagt0.setUsuCveAct(fromHostString(gfvtagt0Model.get().usuCveAct()));
            dclgfvtagt0.setNalCve(fromHostString(gfvtagt0Model.get().nalCve()));
            dclgfvtagt0.setPefCve(fromHostInt(gfvtagt0Model.get().pefCve()));
            dclgfvtagt0.setTcaCve(fromHostString(gfvtagt0Model.get().tcaCve()));
            dclgfvtagt0.setMteCve(fromHostInt(gfvtagt0Model.get().mteCve()));
            dclgfvtagt0.setAgtNumFolInv(fromHostInt(gfvtagt0Model.get().agtNumFolInv()));
            wsGfvtagt0Null.setAgt20(isNull(gfvtagt0Model.get().agtNumFolInv()) ? -1 : 0);
            dclgfvtagt0.setAgtNomExi(fromHostString(gfvtagt0Model.get().agtNomExi()));
            wsGfvtagt0Null.setAgt21(isNull(gfvtagt0Model.get().agtNomExi()) ? -1 : 0);
            dclgfvtagt0.setAgtRstInv(fromHostString(gfvtagt0Model.get().agtRstInv()));
            wsGfvtagt0Null.setAgt22(isNull(gfvtagt0Model.get().agtRstInv()) ? -1 : 0);
            dclgfvtagt0.setAgtFecIniInv(fromHostString(gfvtagt0Model.get().agtFecIniInv()));
            wsGfvtagt0Null.setAgt23(isNull(gfvtagt0Model.get().agtFecIniInv()) ? -1 : 0);
            dclgfvtagt0.setAgtFecFinInv(fromHostString(gfvtagt0Model.get().agtFecFinInv()));
            wsGfvtagt0Null.setAgt24(isNull(gfvtagt0Model.get().agtFecFinInv()) ? -1 : 0);
            dclgfvtagt0.setAgtNomDphInv(fromHostString(gfvtagt0Model.get().agtNomDphInv()));
            wsGfvtagt0Null.setAgt25(isNull(gfvtagt0Model.get().agtNomDphInv()) ? -1 : 0);
            dclgfvtagt0.setAgtFecReh(fromHostString(gfvtagt0Model.get().agtFecReh()));
            wsGfvtagt0Null.setAgt26(isNull(gfvtagt0Model.get().agtFecReh()) ? -1 : 0);
            dclgfvtagt0.setAgtFecMotEss(fromHostString(gfvtagt0Model.get().agtFecMotEss()));
            wsGfvtagt0Null.setAgt27(isNull(gfvtagt0Model.get().agtFecMotEss()) ? -1 : 0);
            dclgfvtagt0.setAgtFecMotAsgIn(fromHostString(gfvtagt0Model.get().agtFecMotAsgIn()));
            wsGfvtagt0Null.setAgt28(isNull(gfvtagt0Model.get().agtFecMotAsgIn()) ? -1 : 0);
            dclgfvtagt0.setAgtObs(fromHostString(gfvtagt0Model.get().agtObs()));
            wsGfvtagt0Null.setAgt29(isNull(gfvtagt0Model.get().agtObs()) ? -1 : 0);
            dclgfvtagt0.setAgtNip(fromHostInt(gfvtagt0Model.get().agtNip()));
            wsGfvtagt0Null.setAgt30(isNull(gfvtagt0Model.get().agtNip()) ? -1 : 0);
            dclgfvtagt0.setAgtNumFacIso(fromHostInt(gfvtagt0Model.get().agtNumFacIso()));
            wsGfvtagt0Null.setAgt31(isNull(gfvtagt0Model.get().agtNumFacIso()) ? -1 : 0);
            dclgfvtagt0.setAgtImpFacIso(fromHostBigDecimal(gfvtagt0Model.get().agtImpFacIso()));
            wsGfvtagt0Null.setAgt32(isNull(gfvtagt0Model.get().agtImpFacIso()) ? -1 : 0);
        }
        //                                                                  12810000
        db2check();
        //                                                                  12830000
        if (this.isDb2Ok()) {
            wsCampos.setWsCuantosAgt(wsCampos.getWsCuantosAgt() + 1);
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinAgt(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 2100-CERRAR-GFVTAGT0.
     *
     * CIERRO EL CURSOR DE AGENTES.
     */
    void p2100CerrarGfvtagt0() {
        //                                                                  13000000
        wsDatosAbend.setWsProcesoLlama("2100-CERRAR-GFVTAGT0          ");

        wsDatosAbend.setWsProcesoEjecuta("2100-CERRAR-GFVTAGT0          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTAGT0        ");

        //                                                                  13050000
        //                                                                  13060000
        if (gfvcagt0Cursor != null) {
            gfvcagt0Cursor.close();
        }
        //                                                                  13100000
        db2check();
        //                                                                  13120000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 3000-PROCESO-AGENTES.
     *
     * PROCESO TODOS LOS AGENTES.
     */
    void p3000ProcesoAgentes() {
        //                                                                  13260000
        //     DISPLAY 'LEI ==> AGENTES              '                      13270000
        //     DISPLAY 'AGT_IDR          GFVTAGT0 => '                      13280000
        //              AGT-IDR          OF  DCLGFVTAGT0                    13290000
        //                                                                  13300000
        //                                                                  13310000
        dclgfvtcna0.initialize();
        wsIndicadores.setWsFinCna(0);

        //                                                                  13340000
        p7700InicioGfvtcna0();
        while (!(wsIndicadores.getWsFinCna() == 1)) {
            p3200ProcesoGfvtcna0();
        }
        p7700CerrarGfvtcna0();
        //                                                                  13380000
        p2100LeerGfvtagt0();
        wsContadores.setWsCuantos(wsContadores.getWsCuantos() + 1);
        // =>  IF  WS-CUANTOS EQUAL  1000                                   13410000
        // =>      MOVE  1  TO  WS-FIN-AGT.                                 13420000
    }

    /**
     *  Paragraph: 3200-PROCESO-GFVTCNA0.
     *
     * PROCESO CONTRATOS DEL AGENTE
     */
    void p3200ProcesoGfvtcna0() {
        //                                                                  13520000
        dclgfvtfla0.initialize();
        wsIndicadores.setWsFinFla(0);

        // OPTIMIZACIÓN: Cache inteligente para GFVTDRC0
        String cacheKey = dclgfvtagt0.getAgtIdr().toInt() + "_" + dclgfvtcna0.getCnaNum().toInt();

        if (drcCache.containsKey(cacheKey)) {
            // Usar resultado del cache
            drcResultForContract = drcCache.get(cacheKey);
            totalGfvtdrc0CallsOptimized++;
        } else {
            // Ejecutar consulta y guardar en cache
            wsIndicadores.setWsFinDrc(0);
            p7950InicioGfvcdrc0();
            p7950LeerGfvcdrc0();
            p7950CerrarGfvcdrc0();

            drcResultForContract = wsIndicadores.getWsFinDrc();
            drcCache.put(cacheKey, drcResultForContract);
            totalGfvtdrc0Calls++;
        }

        wsIndicadores.setWsN(0);

        wsIndicadores.setWsP(0);

        regMaestro.setPkJefeGpoNal(0);
        regMaestro.setPkJefeGpoProv(0);
        //     DISPLAY 'LEI ==> CONTRATOS            '                      13590000
        //     DISPLAY 'AGT_IDR          GFVTAGT0 => '                      13600000
        //              AGT-IDR          OF  DCLGFVTAGT0                    13610000
        //     DISPLAY 'AGT_IDR          GFVTCNA0 => '                      13620000
        //              AGT-IDR          OF  DCLGFVTCNA0                    13630000
        //     DISPLAY 'CNA_NUM          GFVTCNA0 => '                      13640000
        //              CNA-NUM          OF  DCLGFVTCNA0                    13650000
        //                                                                  13660000
        //                                                                  13670000
        
        p7800InicioGfvtfla0();
        while (!(wsIndicadores.getWsFinFla() == 1)) {
            p3300ProcesoGfvtfla0();
        }
        p7800CerrarGfvtfla0();
        //                                                                  13710000
        wsCampos.setWsCuantosCna(wsCampos.getWsCuantosCna() + 1);
        p7700LeerGfvtcna0();
    }

    /**
     *  Paragraph: 3300-PROCESO-GFVTFLA0.
     *
     * PROCESO FOLIOS    DEL AGENTE
     */
    void p3300ProcesoGfvtfla0() {
        //                                                                  13830000
        //     DISPLAY 'LEI ==> FOLIOS               '                      13840000
        //     DISPLAY 'AGT_IDR          GFVTAGT0 => '                      13850000
        //              AGT-IDR          OF  DCLGFVTAGT0                    13860000
        //     DISPLAY 'AGT_IDR          GFVTCNA0 => '                      13870000
        //              AGT-IDR          OF  DCLGFVTCNA0                    13880000
        //     DISPLAY 'CNA_NUM          GFVTCNA0 => '                      13890000
        //              CNA-NUM          OF  DCLGFVTCNA0                    13900000
        //     DISPLAY 'TFP_CVE          GFVTCNA0 => '                      13910000
        //              TFP-CVE          OF  DCLGFVTCNA0                    13920000
        //     DISPLAY 'CNA_GER_ZON_DCM  GFVTCNA0 => '                      13930000
        //              CNA-GER-ZON-DCM  OF  DCLGFVTCNA0                    13940000
        //     DISPLAY 'CNA_GER_ZON_ENV  GFVTCNA0 => '                      13950000
        //              CNA-GER-ZON-ENV  OF  DCLGFVTCNA0                    13960000
        //     DISPLAY 'AGT_IDR          GFVTFLA0 => '                      13970000
        //              AGT-IDR          OF  DCLGFVTFLA0                    13980000
        //     DISPLAY 'CNA_NUM          GFVTFLA0 => '                      13990000
        //              CNA-NUM          OF  DCLGFVTFLA0                    14000000
        //     DISPLAY 'FLA_NUM          GFVTFLA0 => '                      14010000
        //              FLA-NUM          OF  DCLGFVTFLA0                    14020000
        //     DISPLAY 'FLA_GER_ZON      GFVTFLA0 => '                      14030000
        //              FLA-GER-ZON      OF  DCLGFVTFLA0                    14040000
        //     DISPLAY 'ORV_CVE          GFVTFLA0 => '                      14050000
        //              ORV-CVE          OF  DCLGFVTFLA0                    14060000
        //                                                                  14070000
        regMaestro.initialize();
        //                                                                  14090000
        // ==>                                                              14100000
        // ==>     MOVE  FLA-NUM OF DCLGFVTFLA0  TO  WS-FLA-NUM             14110000
        // ==>     MOVE  WS-FLA-NUM1             TO  WCA-AGENTE             14120000
        // ==>     PERFORM  3350-EXISTE-AGENTE                              14130000
        // ==>                                                              14140000
        // ==>     IF  WS-IND-EXISTE  EQUAL  0                              14150000
        // ==>         MOVE  'M'             TO  PK-EXISTE                  14160000
        // ==>     ELSE                                                     14170000
        // ==>         MOVE  'A'             TO  PK-EXISTE                  14180000
        // ==>                                                              14190000
        //                                                                  14200000
        //                                                                  14210000
        wsCampos.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
        //                                                                  14230000
        if (wsCampos.getWsFlaNum0().isEqual("N")) {
            p3400RegresoCamposNac();
        }
        if (wsCampos.getWsFlaNum0().isEqual("P")) {
            p3400RegresoCamposProv();
        }
        //                                                                  14280000
        //                                                                  14290000
        p3450RegresoResto();
        wsContadores.setWsGrabados(wsContadores.getWsGrabados() + 1);
        wsContadores.setWsContador(wsContadores.getWsContador() + 1);
        if (wsContadores.getWsContador() == 500) {
            wsContadores.setWsContador(0);

            display("REGISTROS GRABADOS... ==> ", dclgfvtagt0.getAgtIdr(), " ", dclgfvtcna0.getCnaNum(), " ", dclgfvtfla0.getFlaNum(), " ", wsContadores.getWsGrabados());
        }
        maestro.write(regMaestro);
        //                                                                  14410000
        p7800LeerGfvtfla0();
    }

    /**
     *  Paragraph: 3400-REGRESO-CAMPOS-NAC.
     *
     * PROCESO REGRESO DE CAMPOS CON CLAVE-NAL.
     */
    void p3400RegresoCamposNac() {
        //                                                                  14520000
        regMaestro.setPkCompania("N");
        wsCampos.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
        regMaestro.setPkClaveNal(wsCampos.getWsFlaNum1());
        regMaestro.setPkOficinaNal(dclgfvtfla0.getOfnCve());
        regMaestro.setPkMotStatus(dclgfvtfla0.getMteCve());
        regMaestro.setPkZonaNal(dclgfvtfla0.getFlaGerZon());
        //                                                                  14590000
        db2AaaaMmDd.setValue(dclgfvtfla0.getFlaFecMotEss());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFecStatus(wsFecAammdd);
        regMaestro.setPkFecTerm(wsFecAammdd);
        //                                                                  14660000
        //                                                                  14670000
        wsIndicadores.setWsFinMte(0);

        p7900LeerGfvtmte0();
        if (wsIndicadores.getWsFinMte() == 0) {
            if (dclgfvtmte0.getEsaCve().toInt() == 1) {
                regMaestro.setPkStatus("V");
            } else {
                regMaestro.setPkStatus("C");
            }
        } else {
            regMaestro.setPkStatus("V");
        }
        //                                                                  14770000
        db2AaaaMmDd.setValue(dclgfvtcna0.getCnaFecCnx());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFecConexion(wsFecAammdd);
        regMaestro.setPkFecConexionP(wsFecAammdd);
        //                                                                  14840000
        regMaestro.setPkNumCedula(dclgfvtagt0.getAgtNumCed());
        //                                                                  14860000
        db2AaaaMmDd.setValue(dclgfvtagt0.getAgtFecFinCed());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFinCedula(wsFecAammdd);
        regMaestro.setPkFinCedulaP(wsFecAammdd);
        //                                                                  14930000
        //                                                                  14940000
        //     CUANDO SEA  FISICO(1),  MORAL(2),  GERENTE FISICO(7)         14950000
        //                 Y SEA EL PRIMER FOLIO  DEL CONTRATO              14960000
        //                 EL JEFE DE GRUPO SERA EL PRIMER FOLIO.           14970000
        //     EN ESTE CASO NACIONAL.                                       14980000
        //                                                                  14990000
        //  SE QUITA LA VALIDACION DE TFP-CVE PARA DEJAR AGRUPAR NO         15000007
        //  IMPORTA LA CONDICION                                            15001007
        //     IF  TFP-CVE  OF DCLGFVTCNA0  EQUAL  1  OR  2  OR  7          15010007
        //                                     OR  12                       15011007
        //     IF  WS-N  EQUAL  0                                           15020007
        //         MOVE  1             TO  WS-N                             15030007
        wsIndicadores.setWsFinAfo(0);

        wsCampos.getWsFolLid().setValue(0);
        p3410ObtieneJefes();
        if (wsIndicadores.getWsFinAfo() == 0) {
            regMaestro.setPkJefeGpoNal(wsCampos.getWsFolLid1());
        } else {
            regMaestro.setPkJefeGpoNal(regMaestro.getPkClaveNal());
        }
    }

    /**
     *  Paragraph: 3400-REGRESO-CAMPOS-PROV.
     *
     * PROCESO REGRESO DE CAMPOS CON CLAVE-PROV.
     */
    void p3400RegresoCamposProv() {
        //                                                                  15150000
        regMaestro.setPkCompania("P");
        wsCampos.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
        regMaestro.setPkClaveProv(wsCampos.getWsFlaNum1());
        regMaestro.setPkOficinaProv(dclgfvtfla0.getOfnCve());
        regMaestro.setPkMotStatusProv(dclgfvtfla0.getMteCve());
        regMaestro.setPkZonaProv(dclgfvtfla0.getFlaGerZon());
        //                                                                  15220000
        db2AaaaMmDd.setValue(dclgfvtfla0.getFlaFecMotEss());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFecTerm(wsFecAammdd);
        regMaestro.setPkFecStatus(wsFecAammdd);
        //                                                                  15290000
        wsIndicadores.setWsFinMte(0);

        p7900LeerGfvtmte0();
        if (wsIndicadores.getWsFinMte() == 0) {
            if (dclgfvtmte0.getEsaCve().toInt() == 1) {
                regMaestro.setPkStatusProv("V");
            } else {
                regMaestro.setPkStatusProv("C");
            }
        } else {
            regMaestro.setPkStatusProv("V");
        }
        //                                                                  15390000
        db2AaaaMmDd.setValue(dclgfvtcna0.getCnaFecCnx());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFecConexionP(wsFecAammdd);
        regMaestro.setPkFecConexion(wsFecAammdd);
        //                                                                  15460000
        regMaestro.setPkNumCedulaP(dclgfvtagt0.getAgtNumCed());
        //                                                                  15480000
        db2AaaaMmDd.setValue(dclgfvtagt0.getAgtFecFinCed());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFinCedulaP(wsFecAammdd);
        regMaestro.setPkFinCedula(wsFecAammdd);
        //                                                                  15550000
        //                                                                  15560000
        //                                                                  15570000
        //     CUANDO SEA  FISICO(1),  MORAL(2),  GERENTE FISICO(7)         15580000
        //                 Y SEA EL PRIMER FOLIO  DEL CONTRATO              15590000
        //                 EL JEFE DE GRUPO SERA EL PRIMER FOLIO.           15600000
        //     EN ESTE CASO PROVINCIAL.                                     15610000
        //                                                                  15620000
        //  SE QUITA LA VALIDACION DE TFP-CVE PARA DEJAR AGRUPAR NO         15630007
        //  IMPORTA LA CONDICION                                            15631007
        //     IF  TFP-CVE  OF DCLGFVTCNA0  EQUAL  1  OR  2  OR  7          15640007
        //                                     OR 12                        15641007
        //     IF  WS-P  EQUAL  0                                           15650007
        //         MOVE  1             TO  WS-P                             15660007
        wsIndicadores.setWsFinAfo(0);

        wsCampos.getWsFolLid().setValue(0);
        p3410ObtieneJefes();
        if (wsIndicadores.getWsFinAfo() == 0) {
            regMaestro.setPkJefeGpoProv(wsCampos.getWsFolLid1());
        } else {
            regMaestro.setPkJefeGpoProv(regMaestro.getPkClaveProv());
        }
    }

    /**
     *  Paragraph: 3410-OBTIENE-JEFES.
     *
     * OBTIENE JEFE NACIONAL Y PROVINCIAL DE TABLA GFVTAFO0
     */
    void p3410ObtieneJefes() {
        //                                                                  15706000
        gfvtafo0Model = 
            gfvtafo0Port.select(
                Gfvtafo0Model.builder()
                    .flaNum(toHostString(dclgfvtfla0.getFlaNum()))
                .build());

        if(gfvtafo0Model.isPresent()) {
            gfvtafo0.setFlaNum(fromHostString(gfvtafo0Model.get().flaNum()));
            gfvtafo0.setFolLid(fromHostString(gfvtafo0Model.get().folLid()));
        }
        //                                                                  15709800
        db2check();
        //                                                                  15710000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinAfo(0);

            wsCampos.getWsFolLid().setValue(gfvtafo0.getFolLid());
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinAfo(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: CNA-Y-FLA-CON-IDR-GTE.
     *
     * OBTIENE CNA CON TFP 7,8 Y SUS FOLIOS QUE CORRESPONDEN
     * PARA GENERA PK-GTE-ZONA(NAL, PROV)
     */
    void cnaYFlaConIdrGte() {
        wsIndicadores.setWsFinCna1(0);

        p7960InicioGfvtcna1();
        if (wsIndicadores.getWsFinCna1() == 0) {
            wsIndicadores.setWsFinFla2(0);

            p7970InicioGfvtfla2();
            wsIndicadores.setWsIndNal(0);

            wsIndicadores.setWsIndProv(0);

            while (!(wsIndicadores.getWsFinFla2() == 1)) {
                p3400AccesaGfvtfla2();
            }
            p7970CerrarGfvtfla2();
        } else {
            display("=====>CUIDADO,CUIDADO<===== ");
            display("=====>SIN CNA 7,8    <===== ");
            display("ORV_AGT_IDR_GTE  GCCTORV0 => ", dclgcctorv0.getOrvAgtIdrGte());
            display("ORV_CVE          GCCTORV0 => ", dclgcctorv0.getOrvCve());
            display("AGT_IDR          GFVTAGT0 => ", dclgfvtagt0.getAgtIdr());
            display("AGT_IDR          GFVTCNA0 => ", dclgfvtcna0.getAgtIdr());
            display("CNA_NUM          GFVTCNA0 => ", dclgfvtcna0.getCnaNum());
            display("TFP_CVE          GFVTCNA0 => ", dclgfvtcna0.getTfpCve());
            display("CNA_GER_ZON_DCM  GFVTCNA0 => ", dclgfvtcna0.getCnaGerZonDcm());
            display("CNA_GER_ZON_ENV  GFVTCNA0 => ", dclgfvtcna0.getCnaGerZonEnv());
            display("AGT_IDR          GFVTFLA0 => ", dclgfvtfla0.getAgtIdr());
            display("CNA_NUM          GFVTFLA0 => ", dclgfvtfla0.getCnaNum());
            display("FLA_NUM          GFVTFLA0 => ", dclgfvtfla0.getFlaNum());
            display("FLA_GER_ZON      GFVTFLA0 => ", dclgfvtfla0.getFlaGerZon());
            display("ORV_CVE          GFVTFLA0 => ", dclgfvtfla0.getOrvCve());
        }
        //                                                                  16160000
        p7960CerrarGfvtcna1();
    }

    /**
     *  Paragraph: ZONA-DCM-O-ENV.
     *
     * OBTIENE GTE-ZONA Y NIVEL CON ZON-DCM  O ZON_ENV
     * -Y-
     */
    void zonaDcmOEnv() {
        //                                                                  16260000
        if (wsGfvtcna0Null.getCna34().toInt() < 0) {
            if (wsGfvtcna0Null.getCna33().toInt() < 0) {
                regMaestro.setPkGteZonaNal(99999);
                regMaestro.setPkGteZonaProv(99999);
                regMaestro.setPkNivelNal(99);
                regMaestro.setPkNivelProv(99);
            } else {
                wsCampos.setWsGerZon(dclgfvtcna0.getCnaGerZonEnv().toInt());

                p7950LeerGcctorv0();
                if ((wsIndicadores.getWsFinOrv() == 0) 
                    && (dclgcctorv0.getOrvAgtIdrGte().toInt() != 99999) 
                    && (!dclgcctorv0.getOrvAgtIdrGte().isEqual(dclgfvtagt0.getAgtIdr()))) {
                    // ==>             DISPLAY 'ORV_AGT_IDR_GTE  GCCTORV0 => '          16420000
                    // ==>                      ORV-AGT-IDR-GTE  OF  DCLGCCTORV0        16430000
                    cnaYFlaConIdrGte();
                } else {
                    regMaestro.setPkGteZonaNal(99999);
                    regMaestro.setPkGteZonaProv(99999);
                    regMaestro.setPkNivelNal(99);
                    regMaestro.setPkNivelProv(99);
                }
            }
        } else {
            wsIndicadores.setWsFinDrc(0);

            p7950InicioGfvcdrc0();
            if (wsIndicadores.getWsFinDrc() == 0) {
                wsCampos.setWsGerZon(dclgfvtcna0.getCnaGerZonDcm().toInt());

                // ==>             DISPLAY 'CON ZON_DCM               => '          16630000
                // ==>                      WS-GER-ZON                              16640000
                wsIndicadores.setWsFinOrv(0);

                p7950LeerGcctorv0();
                if ((wsIndicadores.getWsFinOrv() == 0) 
                    && (dclgcctorv0.getOrvAgtIdrGte().toInt() != 99999) 
                    && (!dclgcctorv0.getOrvAgtIdrGte().isEqual(dclgfvtagt0.getAgtIdr()))) {
                    // ==>                 DISPLAY 'ORV_AGT_IDR_GTE  GCCTORV0 => '      16730000
                    // ==>                          ORV-AGT-IDR-GTE  OF  DCLGCCTORV0    16740000
                    cnaYFlaConIdrGte();
                } else {
                    regMaestro.setPkGteZonaNal(99999);
                    regMaestro.setPkGteZonaProv(99999);
                    regMaestro.setPkNivelNal(99);
                    regMaestro.setPkNivelProv(99);
                }
            } else {
                wsCampos.setWsGerZon(dclgfvtcna0.getCnaGerZonEnv().toInt());

                // ==>         DISPLAY 'CON ZON_ENV               => '              17060000
                // ==>                  WS-GER-ZON                                  17070000
                wsIndicadores.setWsFinOrv(0);

                p7950LeerGcctorv0();
                if ((wsIndicadores.getWsFinOrv() == 0) 
                    && (dclgcctorv0.getOrvAgtIdrGte().toInt() != 99999) 
                    && (!dclgcctorv0.getOrvAgtIdrGte().isEqual(dclgfvtagt0.getAgtIdr()))) {
                    // ==>             DISPLAY 'ORV_AGT_IDR_GTE  GCCTORV0 => '          17160000
                    // ==>                      ORV-AGT-IDR-GTE  OF  DCLGCCTORV0        17170000
                    cnaYFlaConIdrGte();
                } else {
                    regMaestro.setPkGteZonaNal(99999);
                    regMaestro.setPkGteZonaProv(99999);
                    regMaestro.setPkNivelNal(99);
                    regMaestro.setPkNivelProv(99);
                }
            }
            p7950CerrarGfvcdrc0();
        }
    }

    /**
     *  Paragraph: 3400-ACCESA-GFVTFLA2.
     *
     * OBTIENE LOS FOLIOS (NAL, PROV).
     */
    void p3400AccesaGfvtfla2() {
        //                                                                  17360000
        wsCampos.getWsFolioxx().setValue(dclgfvtfla0.getFlaNum());
        //         MOVE  FLA-NUM  TO  WS-FOLIOXX                            17380001
        if (wsCampos.getWsFolioxx1().isEqual("N")) {
            if (wsIndicadores.getWsIndNal() == 0) {
                regMaestro.setPkGteZonaNal(wsCampos.getWsFolioxx2());
                regMaestro.setPkNivelNal(5);
                wsIndicadores.setWsIndNal(1);

            } else {
            }
        } else if (wsCampos.getWsFolioxx1().isEqual("P")) {
            if (wsIndicadores.getWsIndProv() == 0) {
                regMaestro.setPkGteZonaProv(wsCampos.getWsFolioxx2());
                regMaestro.setPkNivelProv(5);
                wsIndicadores.setWsIndProv(1);

            }
        }
        //                                                                  17520000
        //                                                                  17530000
        //     DISPLAY 'PK-GTE-ZONA-N/P  GFVCFLA2 => '                      17540000
        //              PK-GTE-ZONA-NAL  ' ' PK-GTE-ZONA-PROV               17550000
        //     DISPLAY 'PK-NIVEL    N/P  GFVCFLA2 => '                      17560000
        //              PK-NIVEL-NAL     ' ' PK-NIVEL-PROV                  17570000
        p7970LeerGfvtfla2();
    }

    /**
     *  Paragraph: TRATAR-CLAVE-ADICIONAL.
     * ASIGNA GERENCIA DE ZONA Y NIVEL A CLAVES ADICIONALES
     */
    void tratarClaveAdicional() {
        wsIndicadores.setWsFinOrv(0);

        wsCampos.setWsGerZon(dclgfvtcna0.getCnaGerZonEnv().toInt());

        p7950LeerGcctorv0();
        if ((wsIndicadores.getWsFinOrv() == 0) 
            && (dclgcctorv0.getOrvAgtIdrGte().toInt() != 99999)) {
            cnaYFlaConIdrGte();
        } else {
            regMaestro.setPkGteZonaNal(99999);
            regMaestro.setPkGteZonaProv(99999);
            regMaestro.setPkNivelNal(99);
            regMaestro.setPkNivelProv(99);
        }
    }

    /**
     *  Paragraph: 3450-REGRESO-RESTO.
     *
     * PROCESO DE CAMPOS CON SITUACION COMUN.
     */
    void p3450RegresoResto() {
        //                                                                  17890000
        regMaestro.setPkCveEnvio(dclgfvtcna0.getCdeCve());
        wsCampos.setWsCodPag(dclgfvtcna0.getCnaCodIpu());
        regMaestro.setPkCvePagoImp(wsCampos.getWsCodPagr());
        regMaestro.setPkPorIva(dclgfvtcna0.getCnaPjeIva()); // [@WARNING overflow pkPorIva(4,2) <- cnaPjeIva(5,2)]
        regMaestro.setPkPorIsr(dclgfvtcna0.getCnaPjeIsr()); // [@WARNING overflow pkPorIsr(4,2) <- cnaPjeIsr(5,2)]
        regMaestro.setPkNumFianza(dclgfvtcna0.getCnaNumFza());
        //                                                                  17960000
        db2AaaaMmDd.setValue(dclgfvtcna0.getCnaFecIniFza());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkIniFianza(wsFecAammdd);
        //                                                                  18020000
        db2AaaaMmDd.setValue(dclgfvtcna0.getCnaFecFinFza());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFinFianza(wsFecAammdd);
        //                                                                  18080000
        regMaestro.setPkCanFianza(dclgfvtcna0.getCnaImpFza()); // [@WARNING overflow pkCanFianza(9,2) <- cnaImpFza(13,2)]
        //                                                                  18100000
        //                                                                  18110000
        if (dclgfvtcna0.getTaaCve().isEqual("1")) {
            regMaestro.setPkTipCedula("P");
        } else {
            regMaestro.setPkTipCedula("D");
        }
        //                                                                  18160000
        //                                                                  18170000
        if (dclgfvtcna0.getAgtIdr().toInt() == 50000) {
            regMaestro.setPkTipo(9);
            regMaestro.setPkGteZonaNal(99999);
            regMaestro.setPkGteZonaProv(99999);
            regMaestro.setPkNivelNal(99);
            regMaestro.setPkNivelProv(99);
        } else if (dclgfvtfla0.getFlaDes().substring(1, 2).isEqual("10")) {
            regMaestro.setPkTipo(10);
        } else {
            regMaestro.setPkTipo(dclgfvtcna0.getTfpCve());
        }
        //                                                                  18290000
        //                                                                  18300000
        // ===========>                                                     18310000
        //     EL PRIMER FOLIO CON  CLAVE UNICA IGUAL A CNA.AGT_IDR_RCL     18320000
        //                       Y  FLA_NEG_CVE IGUAL A 'TV'                18330000
        // ===========>                                                     18340000
        //                                                                  18350000
        //     IF  FLA-DES(1:2)  EQUAL  '10'                                18360000
        //         MOVE  99999   TO  PK-AGENTE-REC                          18370000
        //     ELSE                                                         18380000
        //                                                                  18390000
        //                                                                  18400000
        if (dclgfvtcna0.getAgtIdrRcl().toInt() == 99999) {
            regMaestro.setPkAgenteRec(99999);
        } else {
            wsRegistros.setWsDclgfvtfla00(dclgfvtfla0.toStringValue());

            wsIndicadores.setWsFinFla1(0);

            p7850InicioGfvcfla1();
            if (wsIndicadores.getWsFinFla1() == 0) {
                wsCampos.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
                wsCampos.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
                regMaestro.setPkAgenteRec(wsCampos.getWsFlaNum1());
            } else {
                regMaestro.setPkAgenteRec(0);
                // (SVS                                                             18520100
                p7850InicioGfvcfla3();
                if (wsIndicadores.getWsFinFla3() == 0) {
                    wsCampos.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
                    regMaestro.setPkAgenteRec(wsCampos.getWsFlaNum1());
                } else {
                    regMaestro.setPkAgenteRec(0);
                }
                p7850CierroGfvcfla3();
            }
            //  SVS)                                                            18530200
            p7850CierroGfvcfla1();
            dclgfvtfla0.setValue(wsRegistros.getWsDclgfvtfla00());
        }
        //                                                                  18570000
        //                                                                  18580000
        wsCampos.setWsCodPag(dclgfvtcna0.getCnaCodPag());
        regMaestro.setPkCodPago(wsCampos.getWsCodPagr());
        //                                                                  18610000
        //                                                                  18620000
        wsGfvtcna0Null00.setValue(wsGfvtcna0Null);
        wsRegistros.setWsDclgfvtcna00(dclgfvtcna0.toStringValue());

        wsRegistros.setWsDclgfvtfla00(dclgfvtfla0.toStringValue());

        //                                                                  18660000
        if (dclgfvtcna0.getCnaGerZonEnv().toInt() < 100 
            || dclgfvtfla0.getAgtIdr().toInt() == 50000) {
            regMaestro.setPkGteZonaNal(99999);
            regMaestro.setPkGteZonaProv(99999);
            regMaestro.setPkNivelNal(99);
            regMaestro.setPkNivelProv(99);
        } else if (dclgfvtcna0.getTfpCve().toInt() == 7 
            || dclgfvtcna0.getTfpCve().toInt() == 8 
            || dclgfvtcna0.getTfpCve().toInt() == 11 
            || dclgfvtcna0.getTfpCve().toInt() == 14) {
            // MCG     IF  TFP-CVE  EQUAL  7  OR  8  OR 11                      18750000
            if (wsGfvtcna0Null.getCna34().toInt() < 0) {
                regMaestro.setPkGteZonaNal(99999);
                regMaestro.setPkGteZonaProv(99999);
                regMaestro.setPkNivelNal(99);
                regMaestro.setPkNivelProv(99);
            } else {
                wsCampos.setWsGerZon(dclgfvtcna0.getCnaGerZonDcm().toInt());

                // ==>             DISPLAY 'CON ZON_DCM               => '          18830000
                // ==>                      WS-GER-ZON                              18840000
                wsIndicadores.setWsFinOrv(0);

                p7950LeerGcctorv0();
                if ((wsIndicadores.getWsFinOrv() == 0) 
                    && (dclgcctorv0.getOrvAgtIdrGte().toInt() != 99999) 
                    && (!dclgcctorv0.getOrvAgtIdrGte().isEqual(dclgfvtagt0.getAgtIdr()))) {
                    // ==>                 DISPLAY 'ORV_AGT_IDR_GTE  GCCTORV0 => '      18930000
                    // ==>                          ORV-AGT-IDR-GTE  OF  DCLGCCTORV0    18940000
                    cnaYFlaConIdrGte();
                } else {
                    regMaestro.setPkGteZonaNal(99999);
                    regMaestro.setPkGteZonaProv(99999);
                    regMaestro.setPkNivelNal(99);
                    regMaestro.setPkNivelProv(99);
                }
            }
        } else {
            zonaDcmOEnv();
        }
        //                                                                  19050000
        //                                                                  19060000
        wsGfvtcna0Null.setValue(wsGfvtcna0Null00);
        dclgfvtcna0.setValue(wsRegistros.getWsDclgfvtcna00());
        dclgfvtfla0.setValue(wsRegistros.getWsDclgfvtfla00());
        //                                                                  19100000
        //                                                                  19110000
        if (dclgfvtfla0.getFlaDes().substring(1, 2).isEqual("10")) {
            regMaestro.setPkEsGerente("N");
        } else {
            regMaestro.setPkEsGerente(dclgfvtcna0.getCnaIndGer());
        }
        //                                                                  19160000
        //                                                                  19170000
        if (regMaestro.getPkEsGerente().isEqual("S")) {
            regMaestro.setPkZonaNalGte(dclgfvtfla0.getFlaGerZon());
            regMaestro.setPkZonaProvGte(dclgfvtfla0.getFlaGerZon());
            db2AaaaMmDd.setValue(dclgfvtcna0.getCnaFecAsgGer());
            wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
            wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
            wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
            regMaestro.setPkFecZonaNalGte(wsFecAammdd);
            regMaestro.setPkFecZonaProvGte(wsFecAammdd);
        } else {
            regMaestro.setPkZonaNalGte(0);
            regMaestro.setPkZonaProvGte(0);
            regMaestro.setPkFecZonaNalGte(0);
            regMaestro.setPkFecZonaProvGte(0);
        }
        //                                                                  19340000
        // =======>                                                         19350000
        //     DEPENDIENDO DE TIPO DE DOMICILIO                             19360000
        //                 4 => ARMA DOMICILIO (  C) CON GFVTDMZ0           19370000
        //                 3 => ARMA DOMICILIO (P-C)                        19380000
        //                 2 => ARMA DOMICILIO (  C)                        19390000
        //                 1 => ARMA DOMICILIO (P  )                        19400000
        //                                                                  19410000
        wsCampos.setWsTdmCve(4);

        p7200InicioGfvtdma0();
        if (wsIndicadores.getWsFinDma() == 0) {
            p3500ArmaDmaPobMndEdoE();
            p7200CerrarGfvtdma0();
        } else {
            //                                                                  19480000
            p7200CerrarGfvtdma0();
            wsCampos.setWsTdmCve(3);

            p7200InicioGfvtdma0();
            //                                                                  19520000
            if (wsIndicadores.getWsFinDma() == 0) {
                p3500ArmaDmaPobMndEdoP();
                p3500ArmaDmaPobMndEdoC();
                p7200CerrarGfvtdma0();
            } else {
                p7200CerrarGfvtdma0();
                wsCampos.setWsTdmCve(2);

                p7200InicioGfvtdma0();
                //                                                                  19610000
                if (wsIndicadores.getWsFinDma() == 0) {
                    p3500ArmaDmaPobMndEdoC();
                    p7200CerrarGfvtdma0();
                    wsCampos.setWsTdmCve(1);

                    p7200InicioGfvtdma0();
                    if (wsIndicadores.getWsFinDma() == 0) {
                        p3500ArmaDmaPobMndEdoP();
                    }
                    p7200CerrarGfvtdma0();
                } else {
                    p7200CerrarGfvtdma0();
                    wsCampos.setWsTdmCve(1);

                    p7200InicioGfvtdma0();
                    if (wsIndicadores.getWsFinDma() == 0) {
                        p3500ArmaDmaPobMndEdoP();
                    }
                    p7200CerrarGfvtdma0();
                }
            }
        }
        //                                                                  19790000
        //                                                                  19800000
        p7100LeerGfvtpra0();
        if (wsGfvtpra0Null.getPra02() < 0) {
            regMaestro.getPkNomConyuge().setSpaces();
        } else if (dclgfvtagt0.getPefCve().toInt() == 2) {
            dclgfvtpra0.getPraNomCyg().setSpaces();
        } else {
            generaNombreConyuge();
        }
        // ==>     MOVE  PRA-NOM-CYG  OF DCLGFVTPRA0  TO  PK-NOM-CONYUGE.   19890000
        //                                                                  19900000
        if (dclgfvtagt0.getPefCve().toInt() == 2) {
            regMaestro.getPkSexo().setSpaces();
        } else {
            regMaestro.setPkSexo(dclgfvtpra0.getGsxCve());
        }
        //                                                                  19950000
        if (dclgfvtagt0.getPefCve().toInt() == 2) {
            wsFecAammddr.setWsAa(0);
            wsFecAammddr.setWsMm(0);
            wsFecAammddr.setWsDd(0);
            regMaestro.setPkFecCasam(wsFecAammdd);
        } else if (wsGfvtpra0Null.getPra04() < 0) {
            regMaestro.setPkFecCasam(0);
        } else {
            db2AaaaMmDd.setValue(dclgfvtpra0.getPraFecCas());
            wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
            wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
            wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
            regMaestro.setPkFecCasam(wsFecAammdd);
        }
        //                                                                  20100000
        //                                                                  20110000
        if (dclgfvtagt0.getPefCve().toInt() == 2) {
            regMaestro.setPkEdoCivil(" ");
        } else if (dclgfvtpra0.getEciCve().toInt() == 1) {
            regMaestro.setPkEdoCivil("S");
        } else if (dclgfvtpra0.getEciCve().toInt() == 2) {
            regMaestro.setPkEdoCivil("C");
        } else if (dclgfvtpra0.getEciCve().toInt() == 3) {
            regMaestro.setPkEdoCivil("V");
        } else {
            regMaestro.setPkEdoCivil("D");
        }
        //                                                                  20250000
        //                                                                  20260000
        if (dclgfvtagt0.getPefCve().toInt() == 2) {
            regMaestro.setPkEscolaridad(0);
        } else {
            regMaestro.setPkEscolaridad(dclgfvtpra0.getNveCve());
        }
        //                                                                  20310000
        //                                                                  20320000
        regMaestro.setDatosCons(dclgfvtagt0.getAgtIdr());
        regMaestro.getPkRfc().setValue(dclgfvtagt0.getAgtRfc());
        //                                                                  20350000
        // =>  MOVE  AGT-APE-PAT      OF DCLGFVTAGT0  TO  WS-PK-APE-PAT     20360000
        // =>  MOVE  AGT-APE-MAT      OF DCLGFVTAGT0  TO  WS-PK-APE-MAT     20370000
        // =>  MOVE  AGT-NOM          OF DCLGFVTAGT0  TO  WS-PK-NOM         20380000
        // =>  MOVE  WS-PK-NOMBRE                     TO  PK-NOMBRE.        20390000
        //                                                                  20400000
        if (dclgfvtfla0.getAgtIdr().toInt() == 50000) {
            regMaestro.setPkNombre(dclgfvtfla0.getFlaDes()); // [@WARNING overflow pkNombre(35) <- flaDes(40)]
        } else if (dclgfvtagt0.getPefCve().toInt() == 2) {
            regMaestro.setPkNombre(dclgfvtagt0.getAgtRazSoc()); // [@WARNING overflow pkNombre(35) <- agtRazSoc(60)]
        } else {
            generaNombre();
        }
        //                                                                  20480000
        //                                                                  20490000
        db2AaaaMmDd.setValue(dclgfvtagt0.getAgtFecNac());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkFecNacim(wsFecAammdd);
        //                                                                  20550000
        db2AaaaMmDd.setValue(dclgfvtagt0.getAgtFecIniCed());
        wsFecAammddr.setWsAa(db2AaaaMmDd.getDb2Aa2());
        wsFecAammddr.setWsMm(db2AaaaMmDd.getDb2Mm());
        wsFecAammddr.setWsDd(db2AaaaMmDd.getDb2Dd());
        regMaestro.setPkIniCedula(wsFecAammdd);
        //                                                                  20610000
        if (dclgfvtcna0.getGnaCve().toInt() == 1) {
            regMaestro.setPkOtrasCias("S");
        } else {
            regMaestro.setPkOtrasCias("N");
        }
        regMaestro.setPkCalidad(dclgfvtagt0.getAgtClfLmr());
        regMaestro.setPkRamoP_1(dclgfvtagt0.getTcaCve());
        //                                                                  20680000
        if (dclgfvtagt0.getPefCve().toInt() == 2) {
            regMaestro.setPkNacionalidad(0);
        } else if (dclgfvtagt0.getNalCve().isEqual("MEX")) {
            regMaestro.setPkNacionalidad(1);
        } else {
            regMaestro.setPkNacionalidad(2);
        }
        //                                                                  20760000
        //                                                                  20770000
        
        // OPTIMIZACIÓN: Usar el resultado cacheado de GFVTDRC0 del contrato
        wsIndicadores.setWsFinDrc(drcResultForContract);
        
        //                                                                  20800000
        if (wsIndicadores.getWsFinDrc() == 0) {
            regMaestro.setPkFuente(1);
            regMaestro.setPkGrupo(1);
            regMaestro.setPkSubgrupo(1);
        } else {
            regMaestro.setPkFuente(1);
            regMaestro.setPkGrupo(1);
            regMaestro.setPkSubgrupo(2);
        }
        //                                                                  20890000
        //                                                                  20900000
        if (dclgfvtfla0.getFlaNegCve().isEqual("TV") 
            || dclgfvtfla0.getFlaNegCve().isEqual("G") 
            || dclgfvtfla0.getFlaNegCve().isEqual("GYC")) {
            regMaestro.setPkCveSist("V");
        } else if (dclgfvtfla0.getFlaNegCve().isEqual("TD") 
            || dclgfvtfla0.getFlaNegCve().isEqual("D") 
            || dclgfvtfla0.getFlaNegCve().isEqual("A") 
            || dclgfvtfla0.getFlaNegCve().isEqual("S")) {
            regMaestro.setPkCveSist("D");
        } else {
            // ==>         IF  FLA-NEG-CVE  EQUAL  'TVD'                        20970000
            regMaestro.setPkCveSist("A");
        }
    }

    /**
     *  Paragraph: GENERA-NOMBRE.
     *
     * ARMA NOMBRE UNICO PARA SAETA.
     */
    void generaNombre() {
        wsIndices.setL(1);

        wsCampos.getWsTabPkNombre().setSpaces();
        wsCampos.setWsTabApePat(dclgfvtagt0.getAgtApePat());
        wsCampos.setWsTabApeMat(dclgfvtagt0.getAgtApeMat());
        wsCampos.setWsTabNombre(dclgfvtagt0.getAgtNom());
        //                                                                  21120000
        wsIndices.setJ(1);
        while (!((wsIndices.getJ() > 40
            || wsIndices.getL() > 35))) {
            armaApePat();
            wsIndices.setJ(wsIndices.getJ() + 1);
        }
        //                                                                  21160000
        wsIndices.setJ(1);
        while (!((wsIndices.getJ() > 30
            || wsIndices.getL() > 35))) {
            armaApeMat();
            wsIndices.setJ(wsIndices.getJ() + 1);
        }
        //                                                                  21200000
        wsIndices.setJ(1);
        while (!((wsIndices.getJ() > 40
            || wsIndices.getL() > 35))) {
            armaNombre();
            wsIndices.setJ(wsIndices.getJ() + 1);
        }
        //                                                                  21240000
        regMaestro.setPkNombre(wsCampos.getWsTabPkNombre());
    }

    /**
     *  Paragraph: GENERA-NOMBRE-CONYUGE.
     *
     * ARMA NOMBRE UNICO DE CONYUGE  PARA SAETA.
     */
    void generaNombreConyuge() {
        wsIndices.setL(1);

        wsCampos.getWsTabPkNombre().setSpaces();
        wsCampos.setWsTabApePat(dclgfvtpra0.getPraApePatCyg());
        wsCampos.setWsTabApeMat(dclgfvtpra0.getPraApeMatCyg());
        wsCampos.setWsTabNombre(dclgfvtpra0.getPraNomCyg());
        //                                                                  21390000
        wsIndices.setJ(1);
        while (!((wsIndices.getJ() > 40
            || wsIndices.getL() > 35))) {
            armaApePat();
            wsIndices.setJ(wsIndices.getJ() + 1);
        }
        //                                                                  21430000
        wsIndices.setJ(1);
        while (!((wsIndices.getJ() > 30
            || wsIndices.getL() > 35))) {
            armaApeMat();
            wsIndices.setJ(wsIndices.getJ() + 1);
        }
        //                                                                  21470000
        wsIndices.setJ(1);
        while (!((wsIndices.getJ() > 40
            || wsIndices.getL() > 35))) {
            armaNombre();
            wsIndices.setJ(wsIndices.getJ() + 1);
        }
        //                                                                  21510000
        regMaestro.setPkNomConyuge(wsCampos.getWsTabPkNombre());
    }

    /**
     *  Paragraph: ARMA-APE-PAT.
     *
     */
    void armaApePat() {
        wsIndices.setK(Numeric.add(wsIndices.getJ(), 1).toInt());
        if (wsCampos.getEleApePat().at(wsIndices.getJ()).isSpaces()
            && wsCampos.getEleApePat().at(wsIndices.getK()).isSpaces()) {
            wsCampos.getElePkNombre().at(wsIndices.getL()).setValue(wsCampos.getEleApePat().at(wsIndices.getJ()));
            wsIndices.setL(wsIndices.getL() + 1);
            wsIndices.setJ(41);

        } else {
            wsCampos.getElePkNombre().at(wsIndices.getL()).setValue(wsCampos.getEleApePat().at(wsIndices.getJ()));
            wsIndices.setL(wsIndices.getL() + 1);
        }
    }

    /**
     *  Paragraph: ARMA-APE-MAT.
     *
     */
    void armaApeMat() {
        wsIndices.setK(Numeric.add(wsIndices.getJ(), 1).toInt());
        if (wsCampos.getEleApeMat().at(wsIndices.getJ()).isSpaces()
            && wsCampos.getEleApeMat().at(wsIndices.getK()).isSpaces()) {
            wsCampos.getElePkNombre().at(wsIndices.getL()).setValue(wsCampos.getEleApeMat().at(wsIndices.getJ()));
            wsIndices.setL(wsIndices.getL() + 1);
            wsIndices.setJ(31);

        } else {
            wsCampos.getElePkNombre().at(wsIndices.getL()).setValue(wsCampos.getEleApeMat().at(wsIndices.getJ()));
            wsIndices.setL(wsIndices.getL() + 1);
        }
    }

    /**
     *  Paragraph: ARMA-NOMBRE.
     *
     */
    void armaNombre() {
        wsIndices.setK(Numeric.add(wsIndices.getJ(), 1).toInt());
        if (wsCampos.getEleNombre().at(wsIndices.getJ()).isSpaces()
            && wsCampos.getEleNombre().at(wsIndices.getK()).isSpaces()) {
            wsIndices.setJ(41);

        } else {
            wsCampos.getElePkNombre().at(wsIndices.getL()).setValue(wsCampos.getEleNombre().at(wsIndices.getJ()));
            wsIndices.setL(wsIndices.getL() + 1);
        }
    }

    /**
     *  Paragraph: 3500-ARMA-DMA-POB-MND-EDO-P.
     *
     * ARMA DATOS CON DOMICILIO, POBLACION, MUNICIPIO Y ESTADO
     */
    void p3500ArmaDmaPobMndEdoP() {
        //                                                                  21950000
        p7300LeerGcctpob0();
        //                                                                  21970000
        if (wsIndicadores.getWsFinPob() == 0) {
            p7400LeerGcctmnd0();
            //                                                                  22000000
            if (wsIndicadores.getWsFinMnd() == 0) {
                p7500LeerGcctedo0();
            }
        }
        //                                                                  22030000
        //                                                                  22040000
        if (wsIndicadores.getWsFinEdo() == 0 
            && wsIndicadores.getWsFinMnd() == 0) {
            wsCampos.setWsPkEstado(dclgcctedo0.getEdoCve());
            regMaestro.setPkEstadoP(wsCampos.getWsPkEstado1());
            if (dclgcctedo0.getPaiCve().isEqual("MEX")) {
                regMaestro.setPkPaisP(1);
            } else {
                regMaestro.setPkPaisP(2);
            }
        } else {
            regMaestro.setPkPaisP(0);
            regMaestro.setPkEstadoP(0);
        }
        //                                                                  22160000
        //                                                                  22170000
        if (wsIndicadores.getWsFinPob() == 0) {
            regMaestro.setPkPoblP(dclgcctpob0.getPobNom()); // [@WARNING overflow pkPoblP(25) <- pobNom(40)]
        } else {
            regMaestro.setPkPoblP("SIN POBLACION");
        }
        //                                                                  22220000
        //                                                                  22230000
        wsCampos.setWsPkCalle(gfvtdma0.getDmaCae()); // [@WARNING overflow wsPkCalle(25) <- dmaCae(40)]
        wsCampos.setWsPkNum(gfvtdma0.getDmaNum());
        wsCampos.setWsPkInt(gfvtdma0.getDmaInt());
        regMaestro.setPkCalleP(wsCampos.getWsCalleNumIn());
        wsCampos.getWsCalleNumIn().setSpaces();
        regMaestro.setPkColoniaP(gfvtdma0.getDmaCol()); // [@WARNING overflow pkColoniaP(20) <- dmaCol(40)]
        regMaestro.setPkCodPostP(gfvtdma0.getCpoCve());
        //                                                                  22310000
        //                                                                  22320000
        p8000LeerGfvtcma0();
        if (wsIndicadores.getWsFinCma() == 0) {
            wsCampos.setWsPkCmaDes(dclgfvtcma0.getCmaDes());
            regMaestro.setPkTelefP(wsCampos.getWsPkTel());
        } else {
            regMaestro.getPkTelefP().setValue(0);
        }
    }

    /**
     *  Paragraph: 3500-ARMA-DMA-POB-MND-EDO-C.
     *
     * ARMA DATOS CON DOMICILIO, POBLACION, MUNICIPIO Y ESTADO
     */
    void p3500ArmaDmaPobMndEdoC() {
        //                                                                  22470000
        p7300LeerGcctpob0();
        //                                                                  22490000
        if (wsIndicadores.getWsFinPob() == 0) {
            p7400LeerGcctmnd0();
            //                                                                  22520000
            if (wsIndicadores.getWsFinMnd() == 0) {
                p7500LeerGcctedo0();
            }
        }
        //                                                                  22550000
        //                                                                  22560000
        if (wsIndicadores.getWsFinEdo() == 0 
            && wsIndicadores.getWsFinMnd() == 0) {
            wsCampos.setWsPkEstado(dclgcctedo0.getEdoCve());
            regMaestro.setPkEstadoC(wsCampos.getWsPkEstado1());
            if (dclgcctedo0.getPaiCve().isEqual("MEX")) {
                regMaestro.setPkPaisC(1);
            } else {
                regMaestro.setPkPaisC(2);
            }
        } else {
            regMaestro.setPkPaisC(0);
            regMaestro.setPkEstadoC(0);
        }
        //                                                                  22680000
        //                                                                  22690000
        if (wsIndicadores.getWsFinPob() == 0) {
            regMaestro.setPkPoblC(dclgcctpob0.getPobNom()); // [@WARNING overflow pkPoblC(20) <- pobNom(40)]
        } else {
            regMaestro.setPkPoblC("SIN POBLACION");
        }
        //                                                                  22740000
        //                                                                  22750000
        wsCampos.setWsPkCalle(gfvtdma0.getDmaCae()); // [@WARNING overflow wsPkCalle(25) <- dmaCae(40)]
        wsCampos.setWsPkNum(gfvtdma0.getDmaNum());
        wsCampos.setWsPkInt(gfvtdma0.getDmaInt());
        regMaestro.setPkCalleC(wsCampos.getWsCalleNumIn());
        wsCampos.getWsCalleNumIn().setSpaces();
        regMaestro.setPkColoniaC(gfvtdma0.getDmaCol()); // [@WARNING overflow pkColoniaC(20) <- dmaCol(40)]
        regMaestro.setPkCodPostC(gfvtdma0.getCpoCve());
        //                                                                  22830000
        //                                                                  22840000
        p8000LeerGfvtcma0();
        if (wsIndicadores.getWsFinCma() == 0) {
            wsCampos.setWsPkCmaDes(dclgfvtcma0.getCmaDes());
            regMaestro.setPkTelefC(wsCampos.getWsPkTel());
        } else {
            regMaestro.getPkTelefC().setValue(0);
        }
    }

    /**
     *  Paragraph: 3500-ARMA-DMA-POB-MND-EDO-E.
     *
     * ARMA DATOS CON DOMICILIO, POBLACION, MUNICIPIO Y ESTADO
     */
    void p3500ArmaDmaPobMndEdoE() {
        //                                                                  22990000
        wsIndicadores.setWsFinDmz(0);

        p7250LeerGfvtdmz0();
        if (wsIndicadores.getWsFinDmz() == 0) {
            gfvtdma0.setPobCve(dclgfvtdmz0.getPobCve());
            p7300LeerGcctpob0();
            //                                                                  23060000
            if (wsIndicadores.getWsFinPob() == 0) {
                gfvtdma0.setMndCve(dclgfvtdmz0.getMndCve());
                p7400LeerGcctmnd0();
                //                                                                  23110000
                if (wsIndicadores.getWsFinMnd() == 0) {
                    p7500LeerGcctedo0();
                }
            }
        }
        //                                                                  23140000
        //                                                                  23150000
        if (wsIndicadores.getWsFinEdo() == 0 
            && wsIndicadores.getWsFinMnd() == 0) {
            wsCampos.setWsPkEstado(dclgcctedo0.getEdoCve());
            regMaestro.setPkEstadoC(wsCampos.getWsPkEstado1());
            if (dclgcctedo0.getPaiCve().isEqual("MEX")) {
                regMaestro.setPkPaisC(1);
            } else {
                regMaestro.setPkPaisC(2);
            }
        } else {
            regMaestro.setPkPaisC(0);
            regMaestro.setPkEstadoC(0);
        }
        //                                                                  23270000
        //                                                                  23280000
        if (wsIndicadores.getWsFinPob() == 0) {
            regMaestro.setPkPoblC(dclgcctpob0.getPobNom()); // [@WARNING overflow pkPoblC(20) <- pobNom(40)]
        } else {
            regMaestro.setPkPoblC("SIN POBLACION");
        }
        //                                                                  23330000
        //                                                                  23340000
        wsCampos.setWsPkCalle(dclgfvtdmz0.getDmzCae()); // [@WARNING overflow wsPkCalle(25) <- dmzCae(40)]
        wsCampos.setWsPkNum(dclgfvtdmz0.getDmzNum());
        wsCampos.setWsPkInt(dclgfvtdmz0.getDmzInt());
        regMaestro.setPkCalleC(wsCampos.getWsCalleNumIn());
        wsCampos.getWsCalleNumIn().setSpaces();
        regMaestro.setPkColoniaC(dclgfvtdmz0.getDmzCol()); // [@WARNING overflow pkColoniaC(20) <- dmzCol(40)]
        regMaestro.setPkCodPostC(dclgfvtdmz0.getCpoCve());
        regMaestro.setPkTelefC(dclgfvtdmz0.getDmzTelUno());
    }

    /**
     *  Paragraph: 7100-LEER-GFVTPRA0.
     *
     * PERFORM  8000-LEER-GFVTCMA0.
     * IF  WS-FIN-CMA   EQUAL  0
     * MOVE  CMA-DES  OF DCLGFVTCMA0  TO  WS-PK-CMA-DES
     * MOVE  WS-PK-TEL                TO  PK-TELEF-C
     * ELSE
     * MOVE  0                        TO  PK-TELEF-C.
     * LEER LA TABLA  GFVTPRA0  PERSONALES
     */
    void p7100LeerGfvtpra0() {
        //                                                                  23610000
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GFVTPRA0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTPRA0        ");

        //                                                                  23650000
        gfvtpra0Model = 
            gfvtpra0Port.select(
                Gfvtpra0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                .build());

        if(gfvtpra0Model.isPresent()) {
            dclgfvtpra0.setAgtIdr(fromHostInt(gfvtpra0Model.get().agtIdr()));
            dclgfvtpra0.setPraNomCyg(fromHostString(gfvtpra0Model.get().praNomCyg()));
            wsGfvtpra0Null.setPra02(isNull(gfvtpra0Model.get().praNomCyg()) ? -1 : 0);
            dclgfvtpra0.setGsxCve(fromHostString(gfvtpra0Model.get().gsxCve()));
            wsGfvtpra0Null.setPra12(isNull(gfvtpra0Model.get().gsxCve()) ? -1 : 0);
            dclgfvtpra0.setPraFecCas(fromHostString(gfvtpra0Model.get().praFecCas()));
            wsGfvtpra0Null.setPra04(isNull(gfvtpra0Model.get().praFecCas()) ? -1 : 0);
            dclgfvtpra0.setTvvCve(fromHostString(gfvtpra0Model.get().tvvCve()));
            wsGfvtpra0Null.setPra05(isNull(gfvtpra0Model.get().tvvCve()) ? -1 : 0);
            dclgfvtpra0.setOcuCveAnt(fromHostString(gfvtpra0Model.get().ocuCveAnt()));
            wsGfvtpra0Null.setPra14(isNull(gfvtpra0Model.get().ocuCveAnt()) ? -1 : 0);
            dclgfvtpra0.setEciCve(fromHostInt(gfvtpra0Model.get().eciCve()));
            wsGfvtpra0Null.setPra11(isNull(gfvtpra0Model.get().eciCve()) ? -1 : 0);
            dclgfvtpra0.setNveCve(fromHostInt(gfvtpra0Model.get().nveCve()));
            wsGfvtpra0Null.setPra13(isNull(gfvtpra0Model.get().nveCve()) ? -1 : 0);
            dclgfvtpra0.setPraApePatCyg(fromHostString(gfvtpra0Model.get().praApePatCyg()));
            wsGfvtpra0Null.setPra09(isNull(gfvtpra0Model.get().praApePatCyg()) ? -1 : 0);
            dclgfvtpra0.setPraApeMatCyg(fromHostString(gfvtpra0Model.get().praApeMatCyg()));
            wsGfvtpra0Null.setPra10(isNull(gfvtpra0Model.get().praApeMatCyg()) ? -1 : 0);
        }
        //                                                                  23960000
        db2check();
        //                                                                  23980000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinPra(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinPra(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7200-INICIO-GFVTDMA0.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTDMA0
     */
    void p7200InicioGfvtdma0() {
        //                                                                  24150000
        wsDatosAbend.setWsProcesoEjecuta("7200-INICIO-GFVTDMA0          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTDMA0        ");

        //                                                                  24190000
        gfvcdma0Cursor.open();
        //                                                                  24230000
        db2check();
        //                                                                  24250000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDma(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  24300000
        p7200LeerGfvtdma0();
    }

    /**
     *  Paragraph: 7200-LEER-GFVTDMA0.
     *
     * FETCH A GFVTDMA0       DOMICILIOS
     */
    void p7200LeerGfvtdma0() {
        //                                                                  24400000
        wsDatosAbend.setWsProcesoEjecuta("7200-LEER-GFVTDMA0            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTDMA0        ");

        //                                                                  24440000
        gfvtdma0Model = gfvcdma0Cursor.next();

        if (gfvtdma0Model.isPresent()) {
            dclgfvtagt0.setAgtIdr(fromHostInt(gfvtdma0Model.get().agtIdr()));
            gfvtdma0.setDmaIdr(fromHostInt(gfvtdma0Model.get().dmaIdr()));
            gfvtdma0.setDmaCae(fromHostString(gfvtdma0Model.get().dmaCae()));
            wsGfvtpra0Null.setDma03(isNull(gfvtdma0Model.get().dmaCae()) ? -1 : 0);
            gfvtdma0.setDmaNum(fromHostString(gfvtdma0Model.get().dmaNum()));
            wsGfvtpra0Null.setDma04(isNull(gfvtdma0Model.get().dmaNum()) ? -1 : 0);
            gfvtdma0.setDmaInt(fromHostString(gfvtdma0Model.get().dmaInt()));
            wsGfvtpra0Null.setDma05(isNull(gfvtdma0Model.get().dmaInt()) ? -1 : 0);
            gfvtdma0.setDmaCol(fromHostString(gfvtdma0Model.get().dmaCol()));
            gfvtdma0.setTdmCve(fromHostInt(gfvtdma0Model.get().tdmCve()));
            gfvtdma0.setPobCve(fromHostInt(gfvtdma0Model.get().pobCve()));
            wsGfvtpra0Null.setDma08(isNull(gfvtdma0Model.get().pobCve()) ? -1 : 0);
            gfvtdma0.setCpoCve(fromHostInt(gfvtdma0Model.get().cpoCve()));
            gfvtdma0.setMndCve(fromHostInt(gfvtdma0Model.get().mndCve()));
            wsGfvtpra0Null.setDma10(isNull(gfvtdma0Model.get().mndCve()) ? -1 : 0);
            gfvtdma0.setDmaDirExt(fromHostString(gfvtdma0Model.get().dmaDirExt()));
            wsGfvtpra0Null.setDma11(isNull(gfvtdma0Model.get().dmaDirExt()) ? -1 : 0);
        }
        //                                                                  24610000
        db2check();
        //                                                                  24630000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDma(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinDma(1);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  24710000
    }

    /**
     *  Paragraph: 7200-CERRAR-GFVTDMA0.
     *
     */
    void p7200CerrarGfvtdma0() {
        //                                                                  24770000
        wsDatosAbend.setWsProcesoEjecuta("7200-CERRAR-GFVTDMA0          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTDMA0        ");

        //                                                                  24810000
        if (gfvcdma0Cursor != null) {
            gfvcdma0Cursor.close();
        }
        //                                                                  24850000
        db2check();
        //                                                                  24870000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDma(0);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7250-LEER-GFVTDMZ0.
     *
     * LEER  TABLA  GFVTDMZ0
     */
    void p7250LeerGfvtdmz0() {
        //                                                                  25030000
        wsDatosAbend.setWsProcesoEjecuta("7250-LEER-GFVTDMZ0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTDMZ0        ");

        //                                                                  25070000
        gfvtdmz0Model = 
            gfvtdmz0Port.findByOrvCve(
                                    dclgfvtcna0.getCnaGerZonEnv().toInt());

        if(gfvtdmz0Model.isPresent()) {
            dclgfvtdmz0.setOrvCve(fromHostInt(gfvtdmz0Model.get().orvCve()));
            dclgfvtdmz0.setDmzCae(fromHostString(gfvtdmz0Model.get().dmzCae()));
            dclgfvtdmz0.setDmzNum(fromHostString(gfvtdmz0Model.get().dmzNum()));
            dclgfvtdmz0.setDmzInt(fromHostString(gfvtdmz0Model.get().dmzInt()));
            dclgfvtdmz0.setDmzCol(fromHostString(gfvtdmz0Model.get().dmzCol()));
            dclgfvtdmz0.setDmzTelUno(fromHostBigDecimal(gfvtdmz0Model.get().dmzTelUno()));
            dclgfvtdmz0.setDmzTelDos(fromHostBigDecimal(gfvtdmz0Model.get().dmzTelDos()));
            dclgfvtdmz0.setPobCve(fromHostInt(gfvtdmz0Model.get().pobCve()));
            dclgfvtdmz0.setCpoCve(fromHostInt(gfvtdmz0Model.get().cpoCve()));
            dclgfvtdmz0.setMndCve(fromHostInt(gfvtdmz0Model.get().mndCve()));
        }
        //                                                                  25410000
        //                                                                  25420000
        db2check();
        //                                                                  25440000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDmz(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinDmz(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7300-LEER-GCCTPOB0.
     *
     * LEER  TABLA  GCCTPOB0
     */
    void p7300LeerGcctpob0() {
        //                                                                  25610000
        wsDatosAbend.setWsProcesoEjecuta("7300-LEER-GCCTPOB0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GCCTPOB0        ");

        //                                                                  25650000
        gcctpob0Model = 
            gcctpob0Port.findByPobCve(
                                    gfvtdma0.getPobCve().toInt());

        if(gcctpob0Model.isPresent()) {
            dclgcctpob0.setPobCve(fromHostInt(gcctpob0Model.get().pobCve()));
            dclgcctpob0.setPobNom(fromHostString(gcctpob0Model.get().pobNom()));
            wsGcctpob0Null.setPob02(isNull(gcctpob0Model.get().pobNom()) ? -1 : 0);
            dclgcctpob0.setFecUltAct(fromHostString(gcctpob0Model.get().fecUltAct()));
            dclgcctpob0.setUsuCveAct(fromHostString(gcctpob0Model.get().usuCveAct()));
        }
        //                                                                  25870000
        //                                                                  25880000
        db2check();
        //                                                                  25900000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinPob(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinPob(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7400-LEER-GCCTMND0.
     *
     * LEER LA TABLA  GCCTMND0  MUNICIPIO
     */
    void p7400LeerGcctmnd0() {
        //                                                                  26070000
        wsDatosAbend.setWsProcesoEjecuta("7400-LEER-GCCTMND0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GCCTMND0        ");

        //                                                                  26110000
        gcctmnd0Model = 
            gcctmnd0Port.findByMndCve(
                                    gfvtdma0.getMndCve().toInt());

        if(gcctmnd0Model.isPresent()) {
            dclgcctmnd0.setMndCve(fromHostInt(gcctmnd0Model.get().mndCve()));
            dclgcctmnd0.setMndNom(fromHostString(gcctmnd0Model.get().mndNom()));
            wsGfvtmnd0Null.setMnd02(isNull(gcctmnd0Model.get().mndNom()) ? -1 : 0);
            dclgcctmnd0.setFecUltAct(fromHostString(gcctmnd0Model.get().fecUltAct()));
            dclgcctmnd0.setUsuCveAct(fromHostString(gcctmnd0Model.get().usuCveAct()));
            dclgcctmnd0.setEdoCve(fromHostString(gcctmnd0Model.get().edoCve()));
        }
        //                                                                  26320000
        db2check();
        //                                                                  26340000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinMnd(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinMnd(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7500-LEER-GCCTEDO0.
     *
     * LEER LA TABLA  GCCTEDO0  ESTADO.
     */
    void p7500LeerGcctedo0() {
        //                                                                  26510000
        wsDatosAbend.setWsProcesoEjecuta("7500-LEER-GCCTEDO0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GCCTEDO0        ");

        //                                                                  26550000
        gcctedo0Model = 
            gcctedo0Port.findByEdoCve(
                                    toHostString(dclgcctmnd0.getEdoCve()));

        if(gcctedo0Model.isPresent()) {
            dclgcctedo0.setEdoCve(fromHostString(gcctedo0Model.get().edoCve()));
            dclgcctedo0.setEdoNom(fromHostString(gcctedo0Model.get().edoNom()));
            wsGfvtedo0Null.setEdo02(isNull(gcctedo0Model.get().edoNom()) ? -1 : 0);
            dclgcctedo0.setEdoCodNum(fromHostInt(gcctedo0Model.get().edoCodNum()));
            wsGfvtedo0Null.setEdo03(isNull(gcctedo0Model.get().edoCodNum()) ? -1 : 0);
            dclgcctedo0.setFecUltAct(fromHostString(gcctedo0Model.get().fecUltAct()));
            dclgcctedo0.setUsuCveAct(fromHostString(gcctedo0Model.get().usuCveAct()));
            dclgcctedo0.setPaiCve(fromHostString(gcctedo0Model.get().paiCve()));
        }
        //                                                                  26790000
        db2check();
        //                                                                  26810000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinEdo(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinEdo(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7600-LEER-GFVTCMA0.
     * LEER LA TABLA  GFVTCMA0  COMUNICACION AGT.
     */
    void p7600LeerGfvtcma0() {
        //                                                                  26980000
        wsDatosAbend.setWsProcesoEjecuta("7600-LEER-GFVTCMA0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTCMA0        ");

        //                                                                  27020000
        gfvtcma0Model = 
            gfvtcma0Port.select(
                Gfvtcma0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .dmaIdr(gfvtdma0.getDmaIdr().toInt())
                .build());

        if(gfvtcma0Model.isPresent()) {
            dclgfvtcma0.setCcmCve(fromHostInt(gfvtcma0Model.get().ccmCve()));
            dclgfvtcma0.setAgtIdr(fromHostInt(gfvtcma0Model.get().agtIdr()));
            dclgfvtcma0.setDmaIdr(fromHostInt(gfvtcma0Model.get().dmaIdr()));
            dclgfvtcma0.setCmaDes(fromHostString(gfvtcma0Model.get().cmaDes()));
        }
        //                                                                  27240000
        db2check();
        //                                                                  27260000
        if (this.isDb2Ok()) {
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCma(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7700-INICIO-GFVTCNA0.
     *
     * ABRE CURSOR Y LEE EL PRIMER CONTRATO
     */
    void p7700InicioGfvtcna0() {
        //                                                                  27430000
        wsDatosAbend.setWsProcesoEjecuta("7700-INICIO-GFVTCNA           ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  27470000
        gfvccna0Cursor.open();
        //                                                                  27510000
        db2check();
        //                                                                  27530000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  27580000
        p7700LeerGfvtcna0();
    }

    /**
     *  Paragraph: 7700-LEER-GFVTCNA0.
     *
     * LEER LA TABLA  GFVTCNA0  CONTRATO.
     */
    void p7700LeerGfvtcna0() {
        //                                                                  27690000
        wsDatosAbend.setWsProcesoEjecuta("7700-LEER-GFVTCNA0            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  27730000
        gfvtcna0Model = gfvccna0Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setAgtIdr(fromHostInt(gfvtcna0Model.get().agtIdr()));
            dclgfvtcna0.setEmpCve(fromHostString(gfvtcna0Model.get().empCve()));
            dclgfvtcna0.setCnaNum(fromHostInt(gfvtcna0Model.get().cnaNum()));
            dclgfvtcna0.setCnaTpoCnr(fromHostString(gfvtcna0Model.get().cnaTpoCnr()));
            dclgfvtcna0.setCnaUltCia(fromHostString(gfvtcna0Model.get().cnaUltCia()));
            wsGfvtcna0Null.setCna05(isNull(gfvtcna0Model.get().cnaUltCia()) ? -1 : 0);
            dclgfvtcna0.setCnaFecCnx(fromHostString(gfvtcna0Model.get().cnaFecCnx()));
            dclgfvtcna0.setCnaCodIpu(fromHostString(gfvtcna0Model.get().cnaCodIpu()));
            dclgfvtcna0.setCnaPjeIva(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIva()));
            dclgfvtcna0.setCnaPjeIsr(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIsr()));
            dclgfvtcna0.setCnaComNiv(fromHostString(gfvtcna0Model.get().cnaComNiv()));
            wsGfvtcna0Null.setCna10(isNull(gfvtcna0Model.get().cnaComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaImpComNiv(fromHostBigDecimal(gfvtcna0Model.get().cnaImpComNiv()));
            wsGfvtcna0Null.setCna11(isNull(gfvtcna0Model.get().cnaImpComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaNumFza(fromHostString(gfvtcna0Model.get().cnaNumFza()));
            wsGfvtcna0Null.setCna12(isNull(gfvtcna0Model.get().cnaNumFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecIniFza(fromHostString(gfvtcna0Model.get().cnaFecIniFza()));
            wsGfvtcna0Null.setCna13(isNull(gfvtcna0Model.get().cnaFecIniFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecFinFza(fromHostString(gfvtcna0Model.get().cnaFecFinFza()));
            wsGfvtcna0Null.setCna14(isNull(gfvtcna0Model.get().cnaFecFinFza()) ? -1 : 0);
            dclgfvtcna0.setCnaImpFza(fromHostBigDecimal(gfvtcna0Model.get().cnaImpFza()));
            wsGfvtcna0Null.setCna15(isNull(gfvtcna0Model.get().cnaImpFza()) ? -1 : 0);
            dclgfvtcna0.setCnaIdrFav(fromHostString(gfvtcna0Model.get().cnaIdrFav()));
            dclgfvtcna0.setCnaPjeFav(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeFav()));
            wsGfvtcna0Null.setCna17(isNull(gfvtcna0Model.get().cnaPjeFav()) ? -1 : 0);
            dclgfvtcna0.setPpgCve(fromHostInt(gfvtcna0Model.get().ppgCve()));
            dclgfvtcna0.setCpgCve(fromHostInt(gfvtcna0Model.get().cpgCve()));
            dclgfvtcna0.setCdeCve(fromHostInt(gfvtcna0Model.get().cdeCve()));
            dclgfvtcna0.setOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            dclgfvtcna0.setTaaCve(fromHostString(gfvtcna0Model.get().taaCve()));
            dclgfvtcna0.setGnaCveAgt(fromHostString(gfvtcna0Model.get().gnaCveAgt()));
            dclgfvtcna0.setGnaCve(fromHostInt(gfvtcna0Model.get().gnaCve()));
            dclgfvtcna0.setTfpCve(fromHostInt(gfvtcna0Model.get().tfpCve()));
            dclgfvtcna0.setAgtIdrRcl(fromHostInt(gfvtcna0Model.get().agtIdrRcl()));
            dclgfvtcna0.setCtbNumCta(fromHostString(gfvtcna0Model.get().ctbNumCta()));
            wsGfvtcna0Null.setCna27(isNull(gfvtcna0Model.get().ctbNumCta()) ? -1 : 0);
            dclgfvtcna0.setMteCve(fromHostInt(gfvtcna0Model.get().mteCve()));
            dclgfvtcna0.setMdeCve(fromHostInt(gfvtcna0Model.get().mdeCve()));
            dclgfvtcna0.setZecCve(fromHostInt(gfvtcna0Model.get().zecCve()));
            dclgfvtcna0.setCnaCodPag(fromHostString(gfvtcna0Model.get().cnaCodPag()));
            wsGfvtcna0Null.setCna31(isNull(gfvtcna0Model.get().cnaCodPag()) ? -1 : 0);
            dclgfvtcna0.setCnaFecMotEss(fromHostString(gfvtcna0Model.get().cnaFecMotEss()));
            wsGfvtcna0Null.setCna32(isNull(gfvtcna0Model.get().cnaFecMotEss()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            wsGfvtcna0Null.setCna33(isNull(gfvtcna0Model.get().cnaGerZonEnv()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonDcm(fromHostInt(gfvtcna0Model.get().cnaGerZonDcm()));
            wsGfvtcna0Null.setCna34(isNull(gfvtcna0Model.get().cnaGerZonDcm()) ? -1 : 0);
            dclgfvtcna0.setCnaIndGer(fromHostString(gfvtcna0Model.get().cnaIndGer()));
            wsGfvtcna0Null.setCna35(isNull(gfvtcna0Model.get().cnaIndGer()) ? -1 : 0);
            dclgfvtcna0.setCnaFecAsgGer(fromHostString(gfvtcna0Model.get().cnaFecAsgGer()));
            wsGfvtcna0Null.setCna36(isNull(gfvtcna0Model.get().cnaFecAsgGer()) ? -1 : 0);
        }
        //                                                                  28150000
        db2check();
        //                                                                  28170000
        if (this.isDb2Ok()) {
            wsCampos.setWsCuantosCna(wsCampos.getWsCuantosCna() + 1);
        } else if (this.isDb2Notfnd()) {
            if (wsCampos.getWsCuantosCna() == 0) {
                display("                         ");
                display("NO EXISTE CONTRATOS  ==> ");
                display("AGT-IDR              ==> ", dclgfvtagt0.getAgtIdr());
                wsIndicadores.setWsFinCna(1);

            } else {
                wsCampos.setWsCuantosCna(0);

                wsIndicadores.setWsFinCna(1);

            }
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7700-CERRAR-GFVTCNA0.
     *
     * CIERRO EL CURSOR DE CONTRATOS.
     */
    void p7700CerrarGfvtcna0() {
        //                                                                  28420000
        wsDatosAbend.setWsProcesoEjecuta("7700-CERRAR-GFVTCNA0          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  28460000
        if (gfvccna0Cursor != null) {
            gfvccna0Cursor.close();
        }
        //                                                                  28500000
        db2check();
        //                                                                  28520000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7800-INICIO-GFVTFLA0.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTFLA0
     */
    void p7800InicioGfvtfla0() {
        //                                                                  28660000
        wsDatosAbend.setWsProcesoEjecuta("7800-INICIO-GFVTFLA0          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  28700000
        gfvcfla0Cursor.open();
        //                                                                  28740000
        db2check();
        //                                                                  28760000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  28810000
        p7800LeerGfvtfla0();
    }

    /**
     *  Paragraph: 7800-LEER-GFVTFLA0.
     *
     * FETCH A GFVTFLA0       FOLIOS
     */
    void p7800LeerGfvtfla0() {
        //                                                                  28910000
        wsDatosAbend.setWsProcesoEjecuta("7800-LEER-GFVTFLA0            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  28950000
        gfvtfla0Model = gfvcfla0Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            dclgfvtfla0.setFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            dclgfvtfla0.setFlaDes(fromHostString(gfvtfla0Model.get().flaDes()));
            dclgfvtfla0.setFlaFecIniAsg(fromHostString(gfvtfla0Model.get().flaFecIniAsg()));
            wsGfvtfla0Null.setFla03(isNull(gfvtfla0Model.get().flaFecIniAsg()) ? -1 : 0);
            dclgfvtfla0.setFlaFecFinAsg(fromHostString(gfvtfla0Model.get().flaFecFinAsg()));
            wsGfvtfla0Null.setFla04(isNull(gfvtfla0Model.get().flaFecFinAsg()) ? -1 : 0);
            dclgfvtfla0.setOfnCve(fromHostInt(gfvtfla0Model.get().ofnCve()));
            wsGfvtfla0Null.setFla05(isNull(gfvtfla0Model.get().ofnCve()) ? -1 : 0);
            dclgfvtfla0.setAgtIdr(fromHostInt(gfvtfla0Model.get().agtIdr()));
            dclgfvtfla0.setEmpCve(fromHostString(gfvtfla0Model.get().empCve()));
            dclgfvtfla0.setCnaNum(fromHostInt(gfvtfla0Model.get().cnaNum()));
            dclgfvtfla0.setMteCve(fromHostInt(gfvtfla0Model.get().mteCve()));
            dclgfvtfla0.setOrvCve(fromHostInt(gfvtfla0Model.get().orvCve()));
            dclgfvtfla0.setFlaGerZon(fromHostInt(gfvtfla0Model.get().flaGerZon()));
            wsGfvtfla0Null.setFla11(isNull(gfvtfla0Model.get().flaGerZon()) ? -1 : 0);
            dclgfvtfla0.setFlaNegCve(fromHostString(gfvtfla0Model.get().flaNegCve()));
            wsGfvtfla0Null.setFla12(isNull(gfvtfla0Model.get().flaNegCve()) ? -1 : 0);
            dclgfvtfla0.setFlaFecMotEss(fromHostString(gfvtfla0Model.get().flaFecMotEss()));
            wsGfvtfla0Null.setFla13(isNull(gfvtfla0Model.get().flaFecMotEss()) ? -1 : 0);
        }
        //                                                                  29140000
        db2check();
        //                                                                  29160000
        if (this.isDb2Ok()) {
            wsCampos.setWsCuantosFla(wsCampos.getWsCuantosFla() + 1);
        } else if (this.isDb2Notfnd()) {
            if (wsCampos.getWsCuantosFla() == 0) {
                display("                         ");
                display("NO EXISTE FOLIOS     ==> ");
                display("AGT-IDR              ==> ", dclgfvtagt0.getAgtIdr());
                display("CNA-NUM              ==> ", dclgfvtcna0.getCnaNum());
                wsIndicadores.setWsFinFla(1);

                wsCampos.setWsCuantosFla(0);

            } else {
                wsIndicadores.setWsFinFla(1);

            }
        } else {
            p9999AnalizaSql();
        }
        //                                                                  29340000
    }

    /**
     *  Paragraph: 7800-CERRAR-GFVTFLA0.
     *
     */
    void p7800CerrarGfvtfla0() {
        //                                                                  29400000
        wsDatosAbend.setWsProcesoEjecuta("0800-CERRAR-GFVTFLA0          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  29440000
        if (gfvcfla0Cursor != null) {
            gfvcfla0Cursor.close();
        }
        //                                                                  29480000
        db2check();
        //                                                                  29500000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7850-INICIO-GFVCFLA1.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTFLA1
     */
    void p7850InicioGfvcfla1() {
        //                                                                  29640000
        wsDatosAbend.setWsProcesoEjecuta("7850-INICIO-GFVCFLA1          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  29680000
        gfvcfla1Cursor.open();
        //                                                                  29720000
        db2check();
        //                                                                  29740000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla1(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  29790000
        p7850LeerGfvtfla1();
    }

    /**
     *  Paragraph: 7850-INICIO-GFVCFLA3.
     *
     */
    void p7850InicioGfvcfla3() {
        //                                                                  29823000
        wsDatosAbend.setWsProcesoEjecuta("7850-INICIO-GFVCFLA3          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  29827000
        gfvcfla3Cursor.open();
        //                                                                  29829200
        db2check();
        //                                                                  29829400
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla3(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  29829900
        p7850LeerGfvtfla3();
    }

    /**
     *  Paragraph: 7850-LEER-GFVTFLA1.
     *
     * FETCH A GFVTFLA1       FOLIOS
     */
    void p7850LeerGfvtfla1() {
        //                                                                  29890000
        wsDatosAbend.setWsProcesoEjecuta("7850-LEER-GFVTFLA1            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  29930000
        gfvtfla0Model = gfvcfla1Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            dclgfvtfla0.setFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            dclgfvtfla0.setFlaDes(fromHostString(gfvtfla0Model.get().flaDes()));
            dclgfvtfla0.setFlaFecIniAsg(fromHostString(gfvtfla0Model.get().flaFecIniAsg()));
            wsGfvtfla1Null.setFla033(isNull(gfvtfla0Model.get().flaFecIniAsg()) ? -1 : 0);
            dclgfvtfla0.setFlaFecFinAsg(fromHostString(gfvtfla0Model.get().flaFecFinAsg()));
            wsGfvtfla1Null.setFla044(isNull(gfvtfla0Model.get().flaFecFinAsg()) ? -1 : 0);
            dclgfvtfla0.setOfnCve(fromHostInt(gfvtfla0Model.get().ofnCve()));
            wsGfvtfla1Null.setFla055(isNull(gfvtfla0Model.get().ofnCve()) ? -1 : 0);
            dclgfvtfla0.setAgtIdr(fromHostInt(gfvtfla0Model.get().agtIdr()));
            dclgfvtfla0.setEmpCve(fromHostString(gfvtfla0Model.get().empCve()));
            dclgfvtfla0.setCnaNum(fromHostInt(gfvtfla0Model.get().cnaNum()));
            dclgfvtfla0.setMteCve(fromHostInt(gfvtfla0Model.get().mteCve()));
            dclgfvtfla0.setOrvCve(fromHostInt(gfvtfla0Model.get().orvCve()));
            dclgfvtfla0.setFlaGerZon(fromHostInt(gfvtfla0Model.get().flaGerZon()));
            wsGfvtfla1Null.setFla111(isNull(gfvtfla0Model.get().flaGerZon()) ? -1 : 0);
            dclgfvtfla0.setFlaNegCve(fromHostString(gfvtfla0Model.get().flaNegCve()));
            wsGfvtfla1Null.setFla122(isNull(gfvtfla0Model.get().flaNegCve()) ? -1 : 0);
            dclgfvtfla0.setFlaFecMotEss(fromHostString(gfvtfla0Model.get().flaFecMotEss()));
            wsGfvtfla1Null.setFla133(isNull(gfvtfla0Model.get().flaFecMotEss()) ? -1 : 0);
        }
        //                                                                  30120000
        db2check();
        //                                                                  30140000
        if (this.isDb2Ok()) {
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinFla1(1);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  30220000
    }

    /**
     *  Paragraph: 7850-LEER-GFVTFLA3.
     *
     * FETCH A GFVTFLA1       FOLIOS
     */
    void p7850LeerGfvtfla3() {
        //                                                                  30246000
        wsDatosAbend.setWsProcesoEjecuta("7850-LEER-GFVTFLA3            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  30249100
        gfvtfla0Model = gfvcfla3Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            dclgfvtfla0.setFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            dclgfvtfla0.setFlaDes(fromHostString(gfvtfla0Model.get().flaDes()));
            dclgfvtfla0.setFlaFecIniAsg(fromHostString(gfvtfla0Model.get().flaFecIniAsg()));
            wsGfvtfla1Null.setFla033(isNull(gfvtfla0Model.get().flaFecIniAsg()) ? -1 : 0);
            dclgfvtfla0.setFlaFecFinAsg(fromHostString(gfvtfla0Model.get().flaFecFinAsg()));
            wsGfvtfla1Null.setFla044(isNull(gfvtfla0Model.get().flaFecFinAsg()) ? -1 : 0);
            dclgfvtfla0.setOfnCve(fromHostInt(gfvtfla0Model.get().ofnCve()));
            wsGfvtfla1Null.setFla055(isNull(gfvtfla0Model.get().ofnCve()) ? -1 : 0);
            dclgfvtfla0.setAgtIdr(fromHostInt(gfvtfla0Model.get().agtIdr()));
            dclgfvtfla0.setEmpCve(fromHostString(gfvtfla0Model.get().empCve()));
            dclgfvtfla0.setCnaNum(fromHostInt(gfvtfla0Model.get().cnaNum()));
            dclgfvtfla0.setMteCve(fromHostInt(gfvtfla0Model.get().mteCve()));
            dclgfvtfla0.setOrvCve(fromHostInt(gfvtfla0Model.get().orvCve()));
            dclgfvtfla0.setFlaGerZon(fromHostInt(gfvtfla0Model.get().flaGerZon()));
            wsGfvtfla1Null.setFla111(isNull(gfvtfla0Model.get().flaGerZon()) ? -1 : 0);
            dclgfvtfla0.setFlaNegCve(fromHostString(gfvtfla0Model.get().flaNegCve()));
            wsGfvtfla1Null.setFla122(isNull(gfvtfla0Model.get().flaNegCve()) ? -1 : 0);
            dclgfvtfla0.setFlaFecMotEss(fromHostString(gfvtfla0Model.get().flaFecMotEss()));
            wsGfvtfla1Null.setFla133(isNull(gfvtfla0Model.get().flaFecMotEss()) ? -1 : 0);
        }
        //                                                                  30251000
        db2check();
        //                                                                  30251200
        if (this.isDb2Ok()) {
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinFla3(1);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  30252000
    }

    /**
     *  Paragraph: 7850-CIERRO-GFVCFLA1.
     *
     */
    void p7850CierroGfvcfla1() {
        //                                                                  30280000
        wsDatosAbend.setWsProcesoEjecuta("7850-CIERRO-GFVCFLA1          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  30320000
        if (gfvcfla1Cursor != null) {
            gfvcfla1Cursor.close();
        }
        //                                                                  30360000
        db2check();
        //                                                                  30380000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7850-CIERRO-GFVCFLA3.
     *
     */
    void p7850CierroGfvcfla3() {
        //                                                                  30443000
        wsDatosAbend.setWsProcesoEjecuta("7850-CIERRO-GFVCFLA3          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  30447000
        if (gfvcfla3Cursor != null) {
            gfvcfla3Cursor.close();
        }
        //                                                                  30449200
        db2check();
        //                                                                  30449400
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7900-LEER-GFVTMTE0.
     *
     * LEER LA TABLA  GFVTMTE0  MOTIVO STATUS AGENTE
     */
    void p7900LeerGfvtmte0() {
        //                                                                  30520000
        wsDatosAbend.setWsProcesoEjecuta("7900-LEER-GFVTMTE0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTMTE0        ");

        //                                                                  30560000
        gfvtmte0Model = 
            gfvtmte0Port.findByMteCve(
                                    dclgfvtfla0.getMteCve().toInt());

        if(gfvtmte0Model.isPresent()) {
            dclgfvtmte0.setMteCve(fromHostInt(gfvtmte0Model.get().mteCve()));
            dclgfvtmte0.setMteDes(fromHostString(gfvtmte0Model.get().mteDes()));
            dclgfvtmte0.setFecUltAct(fromHostString(gfvtmte0Model.get().fecUltAct()));
            dclgfvtmte0.setUsuCveAct(fromHostString(gfvtmte0Model.get().usuCveAct()));
            dclgfvtmte0.setEsaCve(fromHostInt(gfvtmte0Model.get().esaCve()));
        }
        //                                                                  30780000
        db2check();
        //                                                                  30800000
        if (this.isDb2Ok()) {
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinMte(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7950-LEER-GCCTORV0.
     *
     * LEER LA TABLA  GFVTORV0  ORGANIZACION VENTAS
     */
    void p7950LeerGcctorv0() {
        //                                                                  30970000
        wsDatosAbend.setWsProcesoEjecuta("7950-LEER-GCCTORV0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GCCTORV0        ");

        //                                                                  31010000
        gcctorv0Model = 
            gcctorv0Port.select(
                Gcctorv0Model.builder()
                    .orvCve(wsCampos.getWsGerZon())
                .build());

        if(gcctorv0Model.isPresent()) {
            dclgcctorv0.setOrvCve(fromHostInt(gcctorv0Model.get().orvCve()));
            dclgcctorv0.setOrvNom(fromHostString(gcctorv0Model.get().orvNom()));
            dclgcctorv0.setOrvNomRes(fromHostString(gcctorv0Model.get().orvNomRes()));
            wsGcctorv0Null.setOrv03(isNull(gcctorv0Model.get().orvNomRes()) ? -1 : 0);
            dclgcctorv0.setFecUltAct(fromHostString(gcctorv0Model.get().fecUltAct()));
            dclgcctorv0.setUsuCveAct(fromHostString(gcctorv0Model.get().usuCveAct()));
            dclgcctorv0.setOrvCveNivSup(fromHostInt(gcctorv0Model.get().orvCveNivSup()));
            wsGcctorv0Null.setOrv06(isNull(gcctorv0Model.get().orvCveNivSup()) ? -1 : 0);
            dclgcctorv0.setEmpCve(fromHostString(gcctorv0Model.get().empCve()));
            dclgcctorv0.setNovCve(fromHostInt(gcctorv0Model.get().novCve()));
            dclgcctorv0.setOrvAgtIdrGte(fromHostInt(gcctorv0Model.get().orvAgtIdrGte()));
            wsGcctorv0Null.setOrv09(isNull(gcctorv0Model.get().orvAgtIdrGte()) ? -1 : 0);
        }
        //                                                                  31320000
        db2check();
        //                                                                  31340000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinOrv(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinOrv(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7950-LEER-GFVTDRC0.
     * LEER LA TABLA  GFVTDRC0  DERECHOS CONTRATO
     */
    void p7950LeerGfvtdrc0() {
        //                                                                  31510000
        wsDatosAbend.setWsProcesoEjecuta("7950-LEER-GFVTDRC0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTDRC0        ");

        //                                                                  31550000
        gfvtdrc0Model = 
            gfvtdrc0Port.select(
                Gfvtdrc0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                .build());

        if(gfvtdrc0Model.isPresent()) {
            dclgfvtdrc0.setDraCve(fromHostInt(gfvtdrc0Model.get().draCve()));
            dclgfvtdrc0.setAgtIdr(fromHostInt(gfvtdrc0Model.get().agtIdr()));
            dclgfvtdrc0.setEmpCve(fromHostString(gfvtdrc0Model.get().empCve()));
            dclgfvtdrc0.setCnaNum(fromHostInt(gfvtdrc0Model.get().cnaNum()));
            dclgfvtdrc0.setDrcNomAut(fromHostString(gfvtdrc0Model.get().drcNomAut()));
            wsGfvtdrc0Null.setDrc05(isNull(gfvtdrc0Model.get().drcNomAut()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecAut(fromHostString(gfvtdrc0Model.get().drcFecAut()));
            wsGfvtdrc0Null.setDrc06(isNull(gfvtdrc0Model.get().drcFecAut()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecIniCio(fromHostString(gfvtdrc0Model.get().drcFecIniCio()));
            wsGfvtdrc0Null.setDrc07(isNull(gfvtdrc0Model.get().drcFecIniCio()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecFinCio(fromHostString(gfvtdrc0Model.get().drcFecFinCio()));
            wsGfvtdrc0Null.setDrc08(isNull(gfvtdrc0Model.get().drcFecFinCio()) ? -1 : 0);
            dclgfvtdrc0.setDrcPjeCom(fromHostBigDecimal(gfvtdrc0Model.get().drcPjeCom()));
            wsGfvtdrc0Null.setDrc09(isNull(gfvtdrc0Model.get().drcPjeCom()) ? -1 : 0);
            dclgfvtdrc0.setDrcIdrAgt(fromHostString(gfvtdrc0Model.get().drcIdrAgt()));
            wsGfvtdrc0Null.setDrc10(isNull(gfvtdrc0Model.get().drcIdrAgt()) ? -1 : 0);
            dclgfvtdrc0.setDrcIdrGte(fromHostString(gfvtdrc0Model.get().drcIdrGte()));
            wsGfvtdrc0Null.setDrc11(isNull(gfvtdrc0Model.get().drcIdrGte()) ? -1 : 0);
        }
        //                                                                  31880000
        db2check();
        //                                                                  31900000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDrc(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinDrc(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7950-INICIO-GFVCDRC0.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTDRC0
     */
    void p7950InicioGfvcdrc0() {
        //                                                                  32070000
        wsDatosAbend.setWsProcesoLlama("7950-INICIO-GFVCDRC0          ");

        wsDatosAbend.setWsProcesoEjecuta("7950-INICIO-GFVCDRC0          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTDRC0        ");

        //                                                                  32120000
        //                                                                  32130000
        gfvcdrc0Cursor.open();
        //                                                                  32170000
        db2check();
        //                                                                  32190000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
        //                                                                  32240000
        p7950LeerGfvcdrc0();
    }

    /**
     *  Paragraph: 7950-LEER-GFVCDRC0.
     *
     * LEER  TABLA  GFVTDRC0
     */
    void p7950LeerGfvcdrc0() {
        //                                                                  32340000
        wsDatosAbend.setWsProcesoLlama("7950-LEER-GFVCDRC0            ");

        wsDatosAbend.setWsProcesoEjecuta("7950-LEER-GFVCDRC0            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTDRC0        ");

        //                                                                  32390000
        gfvtdrc0Model = gfvcdrc0Cursor.next();

        if (gfvtdrc0Model.isPresent()) {
            dclgfvtdrc0.setDraCve(fromHostInt(gfvtdrc0Model.get().draCve()));
            dclgfvtdrc0.setAgtIdr(fromHostInt(gfvtdrc0Model.get().agtIdr()));
            dclgfvtdrc0.setEmpCve(fromHostString(gfvtdrc0Model.get().empCve()));
            dclgfvtdrc0.setCnaNum(fromHostInt(gfvtdrc0Model.get().cnaNum()));
            dclgfvtdrc0.setDrcNomAut(fromHostString(gfvtdrc0Model.get().drcNomAut()));
            wsGfvtdrc0Null.setDrc05(isNull(gfvtdrc0Model.get().drcNomAut()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecAut(fromHostString(gfvtdrc0Model.get().drcFecAut()));
            wsGfvtdrc0Null.setDrc06(isNull(gfvtdrc0Model.get().drcFecAut()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecIniCio(fromHostString(gfvtdrc0Model.get().drcFecIniCio()));
            wsGfvtdrc0Null.setDrc07(isNull(gfvtdrc0Model.get().drcFecIniCio()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecFinCio(fromHostString(gfvtdrc0Model.get().drcFecFinCio()));
            wsGfvtdrc0Null.setDrc08(isNull(gfvtdrc0Model.get().drcFecFinCio()) ? -1 : 0);
            dclgfvtdrc0.setDrcPjeCom(fromHostBigDecimal(gfvtdrc0Model.get().drcPjeCom()));
            wsGfvtdrc0Null.setDrc09(isNull(gfvtdrc0Model.get().drcPjeCom()) ? -1 : 0);
            dclgfvtdrc0.setDrcIdrAgt(fromHostString(gfvtdrc0Model.get().drcIdrAgt()));
            wsGfvtdrc0Null.setDrc10(isNull(gfvtdrc0Model.get().drcIdrAgt()) ? -1 : 0);
            dclgfvtdrc0.setDrcIdrGte(fromHostString(gfvtdrc0Model.get().drcIdrGte()));
            wsGfvtdrc0Null.setDrc11(isNull(gfvtdrc0Model.get().drcIdrGte()) ? -1 : 0);
        }
        //                                                                  32550000
        db2check();
        //                                                                  32570000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDrc(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinDrc(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7950-CERRAR-GFVCDRC0.
     *
     * CIERRO EL CURSOR DE GFVCDRC0
     */
    void p7950CerrarGfvcdrc0() {
        //                                                                  32740000
        wsDatosAbend.setWsProcesoLlama("7950-CERRAR-GFVCDRC0          ");

        wsDatosAbend.setWsProcesoEjecuta("7950-CERRAR-GFVCDRC0          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTDRC0        ");

        //                                                                  32790000
        //                                                                  32800000
        if (gfvcdrc0Cursor != null) {
            gfvcdrc0Cursor.close();
        }
        //                                                                  32840000
        db2check();
        //                                                                  32860000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7960-LEER-GFVTCNA0.
     * LEER LA TABLA  GFVTCNA0  CONTRATOS
     */
    void p7960LeerGfvtcna0() {
        //                                                                  33000000
        wsDatosAbend.setWsProcesoEjecuta("7960-LEER-GFVTCNA0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  33040000
        gfvtcna0Model = 
            gfvtcna0Port.select(
                Gfvtcna0Model.builder()
                    .agtIdr(dclgcctorv0.getOrvAgtIdrGte().toInt())
                .build());

        if(gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setAgtIdr(fromHostInt(gfvtcna0Model.get().agtIdr()));
            dclgfvtcna0.setEmpCve(fromHostString(gfvtcna0Model.get().empCve()));
            dclgfvtcna0.setCnaNum(fromHostInt(gfvtcna0Model.get().cnaNum()));
            dclgfvtcna0.setCnaTpoCnr(fromHostString(gfvtcna0Model.get().cnaTpoCnr()));
            dclgfvtcna0.setCnaUltCia(fromHostString(gfvtcna0Model.get().cnaUltCia()));
            wsGfvtcna0Null.setCna05(isNull(gfvtcna0Model.get().cnaUltCia()) ? -1 : 0);
            dclgfvtcna0.setCnaFecCnx(fromHostString(gfvtcna0Model.get().cnaFecCnx()));
            dclgfvtcna0.setCnaCodIpu(fromHostString(gfvtcna0Model.get().cnaCodIpu()));
            dclgfvtcna0.setCnaPjeIva(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIva()));
            dclgfvtcna0.setCnaPjeIsr(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIsr()));
            dclgfvtcna0.setCnaComNiv(fromHostString(gfvtcna0Model.get().cnaComNiv()));
            wsGfvtcna0Null.setCna10(isNull(gfvtcna0Model.get().cnaComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaImpComNiv(fromHostBigDecimal(gfvtcna0Model.get().cnaImpComNiv()));
            wsGfvtcna0Null.setCna11(isNull(gfvtcna0Model.get().cnaImpComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaNumFza(fromHostString(gfvtcna0Model.get().cnaNumFza()));
            wsGfvtcna0Null.setCna12(isNull(gfvtcna0Model.get().cnaNumFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecIniFza(fromHostString(gfvtcna0Model.get().cnaFecIniFza()));
            wsGfvtcna0Null.setCna13(isNull(gfvtcna0Model.get().cnaFecIniFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecFinFza(fromHostString(gfvtcna0Model.get().cnaFecFinFza()));
            wsGfvtcna0Null.setCna14(isNull(gfvtcna0Model.get().cnaFecFinFza()) ? -1 : 0);
            dclgfvtcna0.setCnaImpFza(fromHostBigDecimal(gfvtcna0Model.get().cnaImpFza()));
            wsGfvtcna0Null.setCna15(isNull(gfvtcna0Model.get().cnaImpFza()) ? -1 : 0);
            dclgfvtcna0.setCnaIdrFav(fromHostString(gfvtcna0Model.get().cnaIdrFav()));
            dclgfvtcna0.setCnaPjeFav(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeFav()));
            wsGfvtcna0Null.setCna17(isNull(gfvtcna0Model.get().cnaPjeFav()) ? -1 : 0);
            dclgfvtcna0.setPpgCve(fromHostInt(gfvtcna0Model.get().ppgCve()));
            dclgfvtcna0.setCpgCve(fromHostInt(gfvtcna0Model.get().cpgCve()));
            dclgfvtcna0.setCdeCve(fromHostInt(gfvtcna0Model.get().cdeCve()));
            dclgfvtcna0.setOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            dclgfvtcna0.setTaaCve(fromHostString(gfvtcna0Model.get().taaCve()));
            dclgfvtcna0.setGnaCveAgt(fromHostString(gfvtcna0Model.get().gnaCveAgt()));
            dclgfvtcna0.setGnaCve(fromHostInt(gfvtcna0Model.get().gnaCve()));
            dclgfvtcna0.setTfpCve(fromHostInt(gfvtcna0Model.get().tfpCve()));
            dclgfvtcna0.setAgtIdrRcl(fromHostInt(gfvtcna0Model.get().agtIdrRcl()));
            dclgfvtcna0.setCtbNumCta(fromHostString(gfvtcna0Model.get().ctbNumCta()));
            wsGfvtcna0Null.setCna27(isNull(gfvtcna0Model.get().ctbNumCta()) ? -1 : 0);
            dclgfvtcna0.setMteCve(fromHostInt(gfvtcna0Model.get().mteCve()));
            dclgfvtcna0.setMdeCve(fromHostInt(gfvtcna0Model.get().mdeCve()));
            dclgfvtcna0.setZecCve(fromHostInt(gfvtcna0Model.get().zecCve()));
            dclgfvtcna0.setCnaCodPag(fromHostString(gfvtcna0Model.get().cnaCodPag()));
            wsGfvtcna0Null.setCna31(isNull(gfvtcna0Model.get().cnaCodPag()) ? -1 : 0);
            dclgfvtcna0.setCnaFecMotEss(fromHostString(gfvtcna0Model.get().cnaFecMotEss()));
            wsGfvtcna0Null.setCna32(isNull(gfvtcna0Model.get().cnaFecMotEss()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            wsGfvtcna0Null.setCna33(isNull(gfvtcna0Model.get().cnaGerZonEnv()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonDcm(fromHostInt(gfvtcna0Model.get().cnaGerZonDcm()));
            wsGfvtcna0Null.setCna34(isNull(gfvtcna0Model.get().cnaGerZonDcm()) ? -1 : 0);
            dclgfvtcna0.setCnaIndGer(fromHostString(gfvtcna0Model.get().cnaIndGer()));
            wsGfvtcna0Null.setCna35(isNull(gfvtcna0Model.get().cnaIndGer()) ? -1 : 0);
            dclgfvtcna0.setCnaFecAsgGer(fromHostString(gfvtcna0Model.get().cnaFecAsgGer()));
            wsGfvtcna0Null.setCna36(isNull(gfvtcna0Model.get().cnaFecAsgGer()) ? -1 : 0);
        }
        //                                                                  33900000
        db2check();
        //                                                                  33920000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna1(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCna1(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7960-INICIO-GFVTCNA1.
     *
     * ABRE CURSOR Y LEE EL PRIMER CONTRATO
     */
    void p7960InicioGfvtcna1() {
        //                                                                  34090000
        wsDatosAbend.setWsProcesoEjecuta("7960-INICIO-GFVTCNA1          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  34130000
        gfvccna1Cursor.open();
        //                                                                  34170000
        db2check();
        //                                                                  34190000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna1(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  34240000
        p7960LeerGfvtcna1();
    }

    /**
     *  Paragraph: 7960-LEER-GFVTCNA1.
     *
     * LEER LA TABLA  GFVTCNA0  CONTRATO.
     */
    void p7960LeerGfvtcna1() {
        //                                                                  34350000
        wsDatosAbend.setWsProcesoEjecuta("7960-LEER-GFVTCNA1            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  34390000
        gfvtcna0Model = gfvccna1Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setAgtIdr(fromHostInt(gfvtcna0Model.get().agtIdr()));
            dclgfvtcna0.setEmpCve(fromHostString(gfvtcna0Model.get().empCve()));
            dclgfvtcna0.setCnaNum(fromHostInt(gfvtcna0Model.get().cnaNum()));
            dclgfvtcna0.setCnaTpoCnr(fromHostString(gfvtcna0Model.get().cnaTpoCnr()));
            dclgfvtcna0.setCnaUltCia(fromHostString(gfvtcna0Model.get().cnaUltCia()));
            wsGfvtcna0Null.setCna05(isNull(gfvtcna0Model.get().cnaUltCia()) ? -1 : 0);
            dclgfvtcna0.setCnaFecCnx(fromHostString(gfvtcna0Model.get().cnaFecCnx()));
            dclgfvtcna0.setCnaCodIpu(fromHostString(gfvtcna0Model.get().cnaCodIpu()));
            dclgfvtcna0.setCnaPjeIva(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIva()));
            dclgfvtcna0.setCnaPjeIsr(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIsr()));
            dclgfvtcna0.setCnaComNiv(fromHostString(gfvtcna0Model.get().cnaComNiv()));
            wsGfvtcna0Null.setCna10(isNull(gfvtcna0Model.get().cnaComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaImpComNiv(fromHostBigDecimal(gfvtcna0Model.get().cnaImpComNiv()));
            wsGfvtcna0Null.setCna11(isNull(gfvtcna0Model.get().cnaImpComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaNumFza(fromHostString(gfvtcna0Model.get().cnaNumFza()));
            wsGfvtcna0Null.setCna12(isNull(gfvtcna0Model.get().cnaNumFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecIniFza(fromHostString(gfvtcna0Model.get().cnaFecIniFza()));
            wsGfvtcna0Null.setCna13(isNull(gfvtcna0Model.get().cnaFecIniFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecFinFza(fromHostString(gfvtcna0Model.get().cnaFecFinFza()));
            wsGfvtcna0Null.setCna14(isNull(gfvtcna0Model.get().cnaFecFinFza()) ? -1 : 0);
            dclgfvtcna0.setCnaImpFza(fromHostBigDecimal(gfvtcna0Model.get().cnaImpFza()));
            wsGfvtcna0Null.setCna15(isNull(gfvtcna0Model.get().cnaImpFza()) ? -1 : 0);
            dclgfvtcna0.setCnaIdrFav(fromHostString(gfvtcna0Model.get().cnaIdrFav()));
            dclgfvtcna0.setCnaPjeFav(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeFav()));
            wsGfvtcna0Null.setCna17(isNull(gfvtcna0Model.get().cnaPjeFav()) ? -1 : 0);
            dclgfvtcna0.setPpgCve(fromHostInt(gfvtcna0Model.get().ppgCve()));
            dclgfvtcna0.setCpgCve(fromHostInt(gfvtcna0Model.get().cpgCve()));
            dclgfvtcna0.setCdeCve(fromHostInt(gfvtcna0Model.get().cdeCve()));
            dclgfvtcna0.setOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            dclgfvtcna0.setTaaCve(fromHostString(gfvtcna0Model.get().taaCve()));
            dclgfvtcna0.setGnaCveAgt(fromHostString(gfvtcna0Model.get().gnaCveAgt()));
            dclgfvtcna0.setGnaCve(fromHostInt(gfvtcna0Model.get().gnaCve()));
            dclgfvtcna0.setTfpCve(fromHostInt(gfvtcna0Model.get().tfpCve()));
            dclgfvtcna0.setAgtIdrRcl(fromHostInt(gfvtcna0Model.get().agtIdrRcl()));
            dclgfvtcna0.setCtbNumCta(fromHostString(gfvtcna0Model.get().ctbNumCta()));
            wsGfvtcna0Null.setCna27(isNull(gfvtcna0Model.get().ctbNumCta()) ? -1 : 0);
            dclgfvtcna0.setMteCve(fromHostInt(gfvtcna0Model.get().mteCve()));
            dclgfvtcna0.setMdeCve(fromHostInt(gfvtcna0Model.get().mdeCve()));
            dclgfvtcna0.setZecCve(fromHostInt(gfvtcna0Model.get().zecCve()));
            dclgfvtcna0.setCnaCodPag(fromHostString(gfvtcna0Model.get().cnaCodPag()));
            wsGfvtcna0Null.setCna31(isNull(gfvtcna0Model.get().cnaCodPag()) ? -1 : 0);
            dclgfvtcna0.setCnaFecMotEss(fromHostString(gfvtcna0Model.get().cnaFecMotEss()));
            wsGfvtcna0Null.setCna32(isNull(gfvtcna0Model.get().cnaFecMotEss()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            wsGfvtcna0Null.setCna33(isNull(gfvtcna0Model.get().cnaGerZonEnv()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonDcm(fromHostInt(gfvtcna0Model.get().cnaGerZonDcm()));
            wsGfvtcna0Null.setCna34(isNull(gfvtcna0Model.get().cnaGerZonDcm()) ? -1 : 0);
            dclgfvtcna0.setCnaIndGer(fromHostString(gfvtcna0Model.get().cnaIndGer()));
            wsGfvtcna0Null.setCna35(isNull(gfvtcna0Model.get().cnaIndGer()) ? -1 : 0);
            dclgfvtcna0.setCnaFecAsgGer(fromHostString(gfvtcna0Model.get().cnaFecAsgGer()));
            wsGfvtcna0Null.setCna36(isNull(gfvtcna0Model.get().cnaFecAsgGer()) ? -1 : 0);
        }
        //                                                                  34810000
        db2check();
        //                                                                  34830000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna1(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCna1(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7960-CERRAR-GFVTCNA1.
     *
     * CIERRO EL CURSOR DE CONTRATOS.
     */
    void p7960CerrarGfvtcna1() {
        //                                                                  35000000
        wsDatosAbend.setWsProcesoEjecuta("7960-CERRAR-GFVTCNA1          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  35040000
        if (gfvccna1Cursor != null) {
            gfvccna1Cursor.close();
        }
        //                                                                  35080000
        db2check();
        //                                                                  35100000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7970-INICIO-GFVTFLA2.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTFLA2
     */
    void p7970InicioGfvtfla2() {
        //                                                                  35260000
        wsDatosAbend.setWsProcesoEjecuta("7970-INICIO-GFVTFLA2          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  35300000
        gfvcfla2Cursor.open();
        //                                                                  35340000
        db2check();
        //                                                                  35360000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla2(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  35410000
        p7970LeerGfvtfla2();
    }

    /**
     *  Paragraph: 7970-LEER-GFVTFLA2.
     *
     * FETCH A GFVTFLA2       FOLIOS
     */
    void p7970LeerGfvtfla2() {
        //                                                                  35510000
        wsDatosAbend.setWsProcesoEjecuta("7970-LEER-GFVTFLA2            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  35550000
        gfvtfla0Model = gfvcfla2Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            dclgfvtfla0.setFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            dclgfvtfla0.setFlaDes(fromHostString(gfvtfla0Model.get().flaDes()));
            dclgfvtfla0.setFlaFecIniAsg(fromHostString(gfvtfla0Model.get().flaFecIniAsg()));
            wsGfvtfla2Null.setFla0333(isNull(gfvtfla0Model.get().flaFecIniAsg()) ? -1 : 0);
            dclgfvtfla0.setFlaFecFinAsg(fromHostString(gfvtfla0Model.get().flaFecFinAsg()));
            wsGfvtfla2Null.setFla0444(isNull(gfvtfla0Model.get().flaFecFinAsg()) ? -1 : 0);
            dclgfvtfla0.setOfnCve(fromHostInt(gfvtfla0Model.get().ofnCve()));
            wsGfvtfla2Null.setFla0555(isNull(gfvtfla0Model.get().ofnCve()) ? -1 : 0);
            dclgfvtfla0.setAgtIdr(fromHostInt(gfvtfla0Model.get().agtIdr()));
            dclgfvtfla0.setEmpCve(fromHostString(gfvtfla0Model.get().empCve()));
            dclgfvtfla0.setCnaNum(fromHostInt(gfvtfla0Model.get().cnaNum()));
            dclgfvtfla0.setMteCve(fromHostInt(gfvtfla0Model.get().mteCve()));
            dclgfvtfla0.setOrvCve(fromHostInt(gfvtfla0Model.get().orvCve()));
            dclgfvtfla0.setFlaGerZon(fromHostInt(gfvtfla0Model.get().flaGerZon()));
            wsGfvtfla2Null.setFla1111(isNull(gfvtfla0Model.get().flaGerZon()) ? -1 : 0);
            dclgfvtfla0.setFlaNegCve(fromHostString(gfvtfla0Model.get().flaNegCve()));
            wsGfvtfla2Null.setFla1222(isNull(gfvtfla0Model.get().flaNegCve()) ? -1 : 0);
            dclgfvtfla0.setFlaFecMotEss(fromHostString(gfvtfla0Model.get().flaFecMotEss()));
            wsGfvtfla2Null.setFla1333(isNull(gfvtfla0Model.get().flaFecMotEss()) ? -1 : 0);
        }
        //                                                                  35740000
        db2check();
        //                                                                  35760000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla2(0);

        //     DISPLAY 'LEI ==> FOLIOS P/ZONA NIVEL  '                      35790000
        //     DISPLAY 'AGT-IDR          GFVCFLA2 => '                      35800000
        //              AGT-IDR          OF  DCLGFVTFLA0                    35810000
        //     DISPLAY 'CNA-NUM          GFVCFLA2 => '                      35820000
        //              CNA-NUM          OF  DCLGFVTFLA0                    35830000
        //     DISPLAY 'FLA-NUM          GFVCFLA2 => '                      35840000
        //              FLA-NUM          OF  DCLGFVTFLA0                    35850000
        //     DISPLAY 'FLA-DES          GFVCFLA2 => '                      35860000
        //              FLA-DES          OF  DCLGFVTFLA0                    35870000
        //     DISPLAY 'FLA-GER-ZON      GFVCFLA2 => '                      35880000
        //              FLA-GER-ZON      OF  DCLGFVTFLA0                    35890000
        //     DISPLAY 'ORV_CVE          GFVCFLA2 => '                      35900000
        //              ORV-CVE          OF  DCLGFVTFLA0                    35910000
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinFla2(1);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  35970000
    }

    /**
     *  Paragraph: 7970-CERRAR-GFVTFLA2.
     *
     */
    void p7970CerrarGfvtfla2() {
        //                                                                  36030000
        wsDatosAbend.setWsProcesoEjecuta("7970-CERRAR-GFVTFLA2          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  36070000
        if (gfvcfla2Cursor != null) {
            gfvcfla2Cursor.close();
        }
        //                                                                  36110000
        db2check();
        //                                                                  36130000
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 8000-LEER-GFVTCMA0.
     *
     * LEER LA TABLA  GFVTCMA0  CANAL DE COMUNICACION
     */
    void p8000LeerGfvtcma0() {
        //                                                                  36270000
        wsDatosAbend.setWsProcesoEjecuta("8000-LEER-GFVTCMA0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTCMA0        ");

        //                                                                  36310000
        gfvtcma0Model = 
            gfvtcma0Port.select_1(
                Gfvtcma0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .dmaIdr(gfvtdma0.getDmaIdr().toInt())
                .build());

        if(gfvtcma0Model.isPresent()) {
            dclgfvtcma0.setCcmCve(fromHostInt(gfvtcma0Model.get().ccmCve()));
            dclgfvtcma0.setAgtIdr(fromHostInt(gfvtcma0Model.get().agtIdr()));
            dclgfvtcma0.setDmaIdr(fromHostInt(gfvtcma0Model.get().dmaIdr()));
            dclgfvtcma0.setCmaDes(fromHostString(gfvtcma0Model.get().cmaDes()));
        }
        //                                                                  36530000
        db2check();
        //                                                                  36550000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCma(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCma(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 8100-LEER-GFVTDRC0.
     *
     * LEER LA TABLA  GFVTDRC0  DERECHOS DE CONTRATO
     */
    void p8100LeerGfvtdrc0() {
        //                                                                  36720000
        wsDatosAbend.setWsProcesoEjecuta("8100-LEER-GFVTDRC0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTDRC0        ");

        //                                                                  36760000
        gfvtdrc0Model = 
            gfvtdrc0Port.select_1(
                Gfvtdrc0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                .build());

        if(gfvtdrc0Model.isPresent()) {
            dclgfvtdrc0.setDraCve(fromHostInt(gfvtdrc0Model.get().draCve()));
            dclgfvtdrc0.setAgtIdr(fromHostInt(gfvtdrc0Model.get().agtIdr()));
            dclgfvtdrc0.setEmpCve(fromHostString(gfvtdrc0Model.get().empCve()));
            dclgfvtdrc0.setCnaNum(fromHostInt(gfvtdrc0Model.get().cnaNum()));
            dclgfvtdrc0.setDrcNomAut(fromHostString(gfvtdrc0Model.get().drcNomAut()));
            wsGfvtdrc0Null.setDrc05(isNull(gfvtdrc0Model.get().drcNomAut()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecAut(fromHostString(gfvtdrc0Model.get().drcFecAut()));
            wsGfvtdrc0Null.setDrc06(isNull(gfvtdrc0Model.get().drcFecAut()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecIniCio(fromHostString(gfvtdrc0Model.get().drcFecIniCio()));
            wsGfvtdrc0Null.setDrc07(isNull(gfvtdrc0Model.get().drcFecIniCio()) ? -1 : 0);
            dclgfvtdrc0.setDrcFecFinCio(fromHostString(gfvtdrc0Model.get().drcFecFinCio()));
            wsGfvtdrc0Null.setDrc08(isNull(gfvtdrc0Model.get().drcFecFinCio()) ? -1 : 0);
            dclgfvtdrc0.setDrcPjeCom(fromHostBigDecimal(gfvtdrc0Model.get().drcPjeCom()));
            wsGfvtdrc0Null.setDrc09(isNull(gfvtdrc0Model.get().drcPjeCom()) ? -1 : 0);
            dclgfvtdrc0.setDrcIdrAgt(fromHostString(gfvtdrc0Model.get().drcIdrAgt()));
            wsGfvtdrc0Null.setDrc10(isNull(gfvtdrc0Model.get().drcIdrAgt()) ? -1 : 0);
            dclgfvtdrc0.setDrcIdrGte(fromHostString(gfvtdrc0Model.get().drcIdrGte()));
            wsGfvtdrc0Null.setDrc11(isNull(gfvtdrc0Model.get().drcIdrGte()) ? -1 : 0);
        }
        //                                                                  37130000
        db2check();
        //                                                                  37150000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDrc(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinDrc(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 4000-FIN.
     *
     * 4000 CIERRRA ARCHIVOS Y DA TOTALES.                            *
     */
    void p4000Fin() {
        maestro.close();
        
        // Calcular tiempo de ejecución
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        // Mostrar métricas de rendimiento
        display("*************************************");
        display("**   MÉTRICAS DE OPTIMIZACIÓN      **");
        display("*************************************");
        display("REGISTROS GRABADOS   ==>   ", wsContadores.getWsGrabados());
        display("CONTRATOS PROCESADOS ==>   ", wsCampos.getWsCuantosCna());
        display("CONSULTAS GFVTDRC0 EJECUTADAS ==> ", totalGfvtdrc0Calls);
        display("CONSULTAS GFVTDRC0 EVITADAS (CACHE) ==> ", totalGfvtdrc0CallsOptimized);
        display("ENTRADAS EN CACHE    ==>   ", drcCache.size());

        if (totalGfvtdrc0Calls + totalGfvtdrc0CallsOptimized > 0) {
            double cacheHitRate = (double) totalGfvtdrc0CallsOptimized / (totalGfvtdrc0Calls + totalGfvtdrc0CallsOptimized) * 100;
            display("TASA DE ACIERTO CACHE ==>  ", String.format("%.2f", cacheHitRate), "%");
        }

        display("TIEMPO DE EJECUCIÓN  ==>   ", executionTime, " ms");
        display("*************************************");
        display("** OPTIMIZACIÓN: Cache inteligente para GFVTDRC0");
        display("** Evita consultas duplicadas por agente+contrato");
        display("*************************************");
    }

    /**
     *  Paragraph: DB2CHECK.
     *
     * DB2CHECK   RUTINA QUE ANALIZA CODIGO VALIDO.                   *
     */
    void db2check() {
        //                                                                  37440000
        db2ReturnCode = sqlca.getSqlcode();

        //                                                                  37460000
        if (this.isDb2Ok() 
            || this.isDb2Notfnd() 
            || this.isDb2Duprec()) {
            ;
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 9999-ANALIZA-SQL.
     *
     * 9999 RUTINA QUE ANALIZA EL CODIGO DE DB2.                      *
     * *****************ANALIZA*****************************************
     */
    void p9999AnalizaSql() {
        //                                                                  37620000
        //                                                                  37630000
        if (sqlca.getSqlcode() < 0) {
            wsCodigos.setWsSqlcode(Numeric.multiply(sqlca.getSqlcode(), (-1)).toInt());
            wsCodigos.setWsDis1("-");
            wsCodigos.setWsDis2(wsCodigos.getWsSqlcode());
        } else {
            wsCodigos.setWsDis1("+");
            wsCodigos.setWsDis2(sqlca.getSqlcode());
        }
        //                                                                  37710000
        //                                                                  37720000
        display("============ ERROR DB2 ============");
        display("  ");
        display("CODIGO DE ERROR        ===> ", wsCodigos.getWsCodError());
        display("INSTRUCCION SQL        ===> ", wsDatosAbend.getWsInstruccion());
        display("RUTINA LLAMADORA       ===> ", wsDatosAbend.getWsProcesoLlama());
        display("RUTINA EJECUTA         ===> ", wsDatosAbend.getWsProcesoEjecuta());
        display("TABLA                  ===> ", wsDatosAbend.getWsTabla());
        display("PROGRAMA               ===> ", wsDatosAbend.getWsPrograma());
        getSqlExecutor().rollback(sqlca);
        //                                                                  37840000
        if (sqlca.getSqlerrml() > 0) {
            display("VARIABLES ERRONEAS    . . .: ", sqlca.getSqlerrmc());
        }
        if (sqlca.getSqlwarn5().equals("W")) {
            display("MANDATO NO EJECUTADO  . . .: ");
            if (sqlca.getSqlwarn6().equals("W")) {
                display("VALOR DE FECHA AJUSTADO . .: ");
            }
            if (sqlca.getSqlwarn9().equals("W")) {
                display("EXCEPCION ARITMET IGNORADA.: ");
            }
        }
        //                                                                  37960000
        throw new GobackException();
        /* [@WARNING Unreachable code]
        throw new StopRunException();
        */
    }

    // Conditionals (88) - DB2-RETURN-CODE
    public boolean isDb2Ok() {
        return this.db2ReturnCode == DB2_OK;
    }

    public void setDb2Ok() {
        this.db2ReturnCode = DB2_OK;
    }

    public boolean isDb2Notfnd() {
        return this.db2ReturnCode == DB2_NOTFND;
    }

    public void setDb2Notfnd() {
        this.db2ReturnCode = DB2_NOTFND;
    }

    public boolean isDb2Duprec() {
        return this.db2ReturnCode == DB2_DUPREC;
    }

    public void setDb2Duprec() {
        this.db2ReturnCode = DB2_DUPREC;
    }

    public boolean isDb2Notuni() {
        return this.db2ReturnCode == DB2_NOTUNI;
    }

    public void setDb2Notuni() {
        this.db2ReturnCode = DB2_NOTUNI;
    }

    public boolean isDb2Datetime() {
        return this.db2ReturnCode == DB2_DATETIME;
    }

    public void setDb2Datetime() {
        this.db2ReturnCode = DB2_DATETIME;
    }

    public boolean isDb2System() {
        return this.db2ReturnCode == DB2_SYSTEM;
    }

    public void setDb2System() {
        this.db2ReturnCode = DB2_SYSTEM;
    }



    @Autowired
    public void setGfvtpra0Port(IGfvtpra0Port gfvtpra0Port) {
        this.gfvtpra0Port = gfvtpra0Port;
        this.gfvtpra0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtmte0Port(IGfvtmte0Port gfvtmte0Port) {
        this.gfvtmte0Port = gfvtmte0Port;
        this.gfvtmte0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctedo0Port(IGcctedo0Port gcctedo0Port) {
        this.gcctedo0Port = gcctedo0Port;
        this.gcctedo0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctorv0Port(IGcctorv0Port gcctorv0Port) {
        this.gcctorv0Port = gcctorv0Port;
        this.gcctorv0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtafo0Port(IGfvtafo0Port gfvtafo0Port) {
        this.gfvtafo0Port = gfvtafo0Port;
        this.gfvtafo0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcna0Port(IGfvtcna0Port gfvtcna0Port) {
        this.gfvtcna0Port = gfvtcna0Port;
        this.gfvtcna0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdrc0Port(IGfvtdrc0Port gfvtdrc0Port) {
        this.gfvtdrc0Port = gfvtdrc0Port;
        this.gfvtdrc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcma0Port(IGfvtcma0Port gfvtcma0Port) {
        this.gfvtcma0Port = gfvtcma0Port;
        this.gfvtcma0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdmz0Port(IGfvtdmz0Port gfvtdmz0Port) {
        this.gfvtdmz0Port = gfvtdmz0Port;
        this.gfvtdmz0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctpob0Port(IGcctpob0Port gcctpob0Port) {
        this.gcctpob0Port = gcctpob0Port;
        this.gcctpob0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctmnd0Port(IGcctmnd0Port gcctmnd0Port) {
        this.gcctmnd0Port = gcctmnd0Port;
        this.gcctmnd0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtfla0Port(IGfvtfla0Port gfvtfla0Port) {
        this.gfvtfla0Port = gfvtfla0Port;
        this.gfvtfla0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdma0Port(IGfvtdma0Port gfvtdma0Port) {
        this.gfvtdma0Port = gfvtdma0Port;
        this.gfvtdma0Port.setProgramContext(getProgramContext());
    }
}
