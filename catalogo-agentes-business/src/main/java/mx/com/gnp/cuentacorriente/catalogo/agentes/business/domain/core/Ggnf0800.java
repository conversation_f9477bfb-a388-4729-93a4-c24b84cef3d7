
/**
 *  Program: Ggnf0800.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.Linea;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.WsDatosAbend;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.WsCodigos;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtfla0cpy.Dclgfvtfla0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtfdc0cpy.Dclgfvtfdc0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcaf0cpy.Dclgfvtcaf0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.WsGfvtfla0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.Tit11;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.Tit21;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.WsTotales;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.WsIndicadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.WsTimestampr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.TbDclgfvtfla0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.TbdDclgfvtfdc0;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtfdc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcaf0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.ISysdummy1Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtfdc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcaf0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggnf0800 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private AlphanumericVar regFolios = new AlphanumericVar(106);            // 01 REG-FOLIOS X(106) UsedAsReferenceParameter
    private Linea linea = new  Linea();                                      // 01 LINEA UsedAsReferenceParameter
//  private String wsWork = "WORKING";                                      // 01 WS-WORK X(20) // [@WARNING variable NOT USED]
    private WsDatosAbend wsDatosAbend = new  WsDatosAbend();                 // 01 WS-DATOS-ABEND
    private int db2ReturnCode;                                              // 01 DB2-RETURN-CODE S9(9) COMPUTATIONAL
    // Level 88 - Conditional names
    private static final int DB2_OK = 0;
    private static final int DB2_NOTFND = 100;
    private static final int DB2_DUPREC = -803;
    private static final int DB2_NOTUNI = -811;
    private static final int DB2_DATETIME = -181;
    private static final int DB2_SYSTEM = -911;

    private WsCodigos wsCodigos = new  WsCodigos();                          // 01 WS-CODIGOS

    // Copy file QA$CBLCPY$GFVTFLA0
    private Dclgfvtfla0 dclgfvtfla0 = new  Dclgfvtfla0();                    // 01 DCLGFVTFLA0
    // End copy file QA$CBLCPY$GFVTFLA0

    // Copy file QA$CBLCPY$GFVTAGC0
//  private Dclgfvtagc0 dclgfvtagc0 = new  Dclgfvtagc0();                        // 01 DCLGFVTAGC0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTAGC0

    // Copy file QA$CBLCPY$GFVTFDC0
    private Dclgfvtfdc0 dclgfvtfdc0 = new  Dclgfvtfdc0();                    // 01 DCLGFVTFDC0
    // End copy file QA$CBLCPY$GFVTFDC0

    // Copy file QA$CBLCPY$GFVTCAF0
    private Dclgfvtcaf0 dclgfvtcaf0 = new  Dclgfvtcaf0();                    // 01 DCLGFVTCAF0
    // End copy file QA$CBLCPY$GFVTCAF0

    // Level 1
    private WsGfvtfla0Null wsGfvtfla0Null = new  WsGfvtfla0Null();           // 01 WS-GFVTFLA0-NULL
//  private WsGfvtagc0Null wsGfvtagc0Null = new  WsGfvtagc0Null();               // 01 WS-GFVTAGC0-NULL // [@WARNING variable NOT USED]
    private Tit11 tit11 = new  Tit11();                                      // 01 TIT11 UsedAsParameter
    private Tit21 tit21 = new  Tit21();                                      // 01 TIT21 UsedAsParameter
    private WsTotales wsTotales = new  WsTotales();                          // 01 WS-TOTALES
    private WsIndicadores wsIndicadores = new  WsIndicadores();              // 01 WS-INDICADORES
//  private WsCurrent wsCurrent = new  WsCurrent();                              // 01 WS-CURRENT // [@WARNING variable NOT USED]
//  private Db2AaaaMmDd db2AaaaMmDd = new  Db2AaaaMmDd();                        // 01 DB2-AAAA-MM-DD // [@WARNING variable NOT USED]
//  private WsFecAaaa wsFecAaaa = new  WsFecAaaa();                              // 01 WS-FEC-AAAA // [@WARNING variable NOT USED]
//  private WsFecAammddr wsFecAammddr = new  WsFecAammddr();                     // 01 WS-FEC-AAMMDDR REDEFINED BY WS-FEC-AAMMDD // [@WARNING variable NOT USED]
//  private UnsignedNumericVar wsFecAammdd = new UnsignedNumericVar(null, this.wsFecAammddr, 6, 0);              // 01 WS-FEC-AAMMDD REDEFINES WS-FEC-AAMMDDR 9(6) // [@WARNING variable NOT USED]
    private String wsTimestamp = "";                                        // 01 WS-TIMESTAMP X(26)
    private WsTimestampr wsTimestampr = new  WsTimestampr();                 // 01 WS-TIMESTAMPR
    private int fsFolios = 0;                                               // 01 FS-FOLIOS 9(02)
    private TbDclgfvtfla0 tbDclgfvtfla0 = new  TbDclgfvtfla0();              // 01 TB-DCLGFVTFLA0 UsedAsReferenceParameter
    private int wkAbriCursor = 0;                                           // 01 WK-ABRI-CURSOR 9(01)
    private int wkInd = 0;                                                  // 01 WK-IND 9(04)
    private int tbdInd = 0;                                                 // 01 TBD-IND 9(04)
    private TbdDclgfvtfdc0 tbdDclgfvtfdc0 = new  TbdDclgfvtfdc0();           // 01 TBD-DCLGFVTFDC0

    private Sqlca sqlca;
    // Files
    // -----> CBA 15/02/2001
    private ISequentialFile folios;
    // <----- CBA
    private ISequentialFile listado;



    // Declare Sql Ports
    private IGfvtfdc0Port gfvtfdc0Port;
    private IGfvtcaf0Port gfvtcaf0Port;
    private ISysdummy1Port sysdummy1Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtfdc0Model> gfvtfdc0Model;
    private Optional<Gfvtcaf0Model> gfvtcaf0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtfdc0Model> gfvcfdc0Cursor;

    public Ggnf0800(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initFiles();
    }

    public void initFiles() {
        // -----> CBA 15/02/2001

        this.folios = createSequentialFile("FOLIOS", regFolios);
        // <----- CBA

        this.listado = createSequentialFile("LISTADO", "GGNF08R1", linea);

        this.folios.onChangeFileStatus(status -> fsFolios = Integer.parseInt(status));

        this.listado.setRecordLength(133);

    }

    public void run() {
        sqlDelayedParagraph();
        start();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(gfvcfdc0Cursor == null) {
            gfvcfdc0Cursor = gfvtfdc0Port.createCursorGfvcfdc0(
                () -> Gfvtfdc0Model.builder()
                    .agtIdr(dclgfvtfla0.getAgtIdr().toInt())
                    .cnaNum(dclgfvtfla0.getCnaNum().toInt())
                .build());
        }
    }

    /**
     *  Paragraph: (START).
     */
    void start() {
        p1000Inicio();
        while (!(wsIndicadores.getWsFinFla() == 1)) {
            p2000ProcesoGfvtfla0();
        }
        p3000Fin();
        throw new GobackException();
    }

    /**
     *  Paragraph: 1000-INICIO.
     *
     * FUNCION INICIAL
     */
    void p1000Inicio() {
        listado.openOutput();
        //                                                                  06720200
        display("*************************************");
        display("**   INICIA EL PROGRAMA GGNF0800   **");
        display("*************************************");
        //                                                                  06720600
        dclgfvtfla0.initialize();
        dclgfvtfdc0.initialize();
        wsIndicadores.setWsFlaNum("");

        //                                                                  06721000
        genericModel = 
            sysdummy1Port.set(
                null);

        if (genericModel.isPresent()) {
            wsTimestamp = fromHostString(genericModel.get().values().get(0));
        }
        //                                                                  06721400
        wsTimestampr.setValue(wsTimestamp);
        //                                                                  06721600
        p6100Enc1();
    }

    /**
     *  Paragraph: 2000-PROCESO-GFVTFLA0.
     *
     * 2000  PROCESO DE FOLIOS                                        *
     */
    void p2000ProcesoGfvtfla0() {
        wsDatosAbend.setWsProcesoLlama("2000-PROCESO-FOLIOS           ");

        p7850InicioGfvcfla0();
        while (!(wsIndicadores.getWsFinFla() == 1)) {
            p3000ProcesoGfvtfdc0();
        }
        p7850CierroGfvcfla0();
    }

    /**
     *  Paragraph: 3000-PROCESO-GFVTFDC0.
     *
     * PROCESO GFVTFDC0.
     */
    void p3000ProcesoGfvtfdc0() {
        //                                                                  06750000
        // -----> CBA1                                                      06760000
        //     PERFORM  7850-INICIO-GFVCFDC0.                               06770000
        if (tbdInd > 0 
            && tbdDclgfvtfdc0.getTbdTabla().at(1).getTbdAgtIdr().isEqual(dclgfvtfla0.getAgtIdr()) 
            && tbdDclgfvtfdc0.getTbdTabla().at(1).getTbdCnaNum().isEqual(dclgfvtfla0.getCnaNum())) {
            ;
        } else {
            wkAbriCursor = 1;

            p7850InicioGfvcfdc0();
        }
        // <----- CBA1                                                      06772000
        wsDatosAbend.setWsProcesoLlama("3000-PROCESO-GFVTFDC0         ");

        // -----> CBA1                                                      06773000
        //     PERFORM  3100-ACTUALIZA-O-ALTA  UNTIL  WS-FIN-FDC  EQUAL  1  06780000
        wkInd = 1;

        while (!(wkInd > tbdInd)) {
            p3100ActualizaOAlta();
        }
        //     PERFORM  7850-CIERRO-GFVCFDC0.                               06790000
        if (wkAbriCursor == 1) {
            p7850CierroGfvcfdc0();
            wkAbriCursor = 0;

        }
        // <----- CBA1                                                      06792000
        //                                                                  06800000
        p7850LeerGfvtfla0();
        wsIndicadores.setCuantos(wsIndicadores.getCuantos() + 1);
    }

    /**
     *  Paragraph: 3100-ACTUALIZA-O-ALTA.
     *
     * ANALIZA SI SE ACTUALIZA O SE DA DE ALTA A
     * GFVTCAF0  CARTERA-AGENTE FACULTAD.
     */
    void p3100ActualizaOAlta() {
        //                                                                  06900400
        // -----> CBA1                                                      06900500
        dclgfvtfdc0.setAgtIdr(tbdDclgfvtfdc0.getTbdTabla().at(wkInd).getTbdAgtIdr());
        dclgfvtfdc0.setEmpCve(tbdDclgfvtfdc0.getTbdTabla().at(wkInd).getTbdEmpCve());
        dclgfvtfdc0.setCnaNum(tbdDclgfvtfdc0.getTbdTabla().at(wkInd).getTbdCnaNum());
        dclgfvtfdc0.setRamCve(tbdDclgfvtfdc0.getTbdTabla().at(wkInd).getTbdRamCve());
        dclgfvtfdc0.setSraCve(tbdDclgfvtfdc0.getTbdTabla().at(wkInd).getTbdSraCve());
        // <----- CBA1                                                      06901600
        wsDatosAbend.setWsProcesoLlama("3500-ACTUALIZA-O-ALTA         ");

        wsIndicadores.setWsFinCaf(0);

        p7100LeerGfvtcaf0();
        if (wsIndicadores.getWsFinCaf() == 0) {
            wsTotales.setTotCafLeidos(wsTotales.getTotCafLeidos() + 1);
        } else {
            p3500AltaGfvtcaf0();
        }
        //                                                                  06902400
        // -----> CBA1                                                      06902500
        //     PERFORM  7850-LEER-GFVTFDC0.                                 06902600
        //     COMENTARIZAR LINEA ANTERIOR                                  06902700
        wkInd += 1;
    }

    /**
     *  Paragraph: 3500-ALTA-GFVTCAF0.
     *
     * <----- CBA1
     * ==> IF  TOT-CAF-GRABADOS  EQUAL  100000
     * ==>     MOVE  1  TO  WS-FIN-FDC
     * ==>                  WS-FIN-FLA.
     * GENERA CON FOLIOS Y FACULTAD DE CONTRATO
     * CARTERA-AGENTE FACULTAD.
     * OJO==>  SOLAMENTE PRODUCE REPORTE DE ACTUALIZACION SIN
     * OJO==>  AFECTAR LA TABLA GFVTCAF0.
     * 3500-ALTA-GFVTCAF0.
     * PERFORM  3600-ARMA-DCLGFVTCAF0.
     * PERFORM  3550-IMP-GFVTCAF0.
     * ADD  1  TO  TOT-CAF-GRABADOS.
     * GENERA CON FOLIOS Y FACULTAD DE CONTRATO
     * CARTERA-AGENTE FACULTAD.
     */
    void p3500AltaGfvtcaf0() {
        //                                                                  06920200
        p3600ArmaDclgfvtcaf0();
        p3550ImpGfvtcaf0();
        //                                                                  06920500
        wsDatosAbend.setWsProcesoEjecuta("3500-INSERTA-GFVTCAF0         ");

        wsDatosAbend.setWsInstruccion("INSERT          ");

        wsDatosAbend.setWsTabla("GFVTCAF0        ");

        //                                                                  06920900
        //                                                                  06921000
        gfvtcaf0Port.insert(
            GenericModel.builder().values(List.of(
                toHostString(dclgfvtcaf0.getFlaNum()),
                toHostString(dclgfvtcaf0.getRamCve()),
                toHostString(dclgfvtcaf0.getSraCve()),
                toHostString(dclgfvtcaf0.getUsuCveAct()),
                toHostString(dclgfvtcaf0.getFecUltAct())))
            .build());
        //                                                                  06922500
        //                                                                  06922600
        db2check();
        //                                                                  06922800
        if (this.isDb2Ok()) {
            wsTotales.setTotCafGrabados(wsTotales.getTotCafGrabados() + 1);
            linea.setD1Obs("OK..");
        } else if (this.isDb2Duprec()) {
            wsTotales.setTotCafDuplicados(wsTotales.getTotCafDuplicados() + 1);
            linea.setD1Obs("CAF.    DUPLICADO");
            p3550ImpGfvtcaf0();
        } else {
            linea.setD1Obs("CAF.    ERROR    ");
            p3550ImpGfvtcaf0();
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 3550-IMP-GFVTCAF0.
     *
     * RUTINA DE IMPRESION DE LA CARGA DE  CARTERA-AGENTES FACULTAD
     */
    void p3550ImpGfvtcaf0() {
        linea.setD1AgtIdr(dclgfvtfla0.getAgtIdr());
        linea.getD1CnaNum().setValue(dclgfvtfla0.getCnaNum());
        linea.setD1FlaNum(dclgfvtcaf0.getFlaNum());
        linea.setD1RamCve(dclgfvtcaf0.getRamCve());
        linea.setD1SraCve(dclgfvtcaf0.getSraCve());
        p6000Imprimir();
    }

    /**
     *  Paragraph: 3600-ARMA-DCLGFVTCAF0.
     *
     * ARMA CAMPOS PARA LA TABLA GFVTCAF0  CARTERA-AGENTES FACULTAD
     */
    void p3600ArmaDclgfvtcaf0() {
        //                                                                  06929000
        dclgfvtcaf0.setFlaNum(dclgfvtfla0.getFlaNum());
        dclgfvtcaf0.setRamCve(dclgfvtfdc0.getRamCve());
        dclgfvtcaf0.setSraCve(dclgfvtfdc0.getSraCve());
        dclgfvtcaf0.setUsuCveAct("TD6NFS");
        dclgfvtcaf0.setFecUltAct(wsTimestampr.getWsFechaTimestamp());
    }

    /**
     *  Paragraph: 6000-IMPRIMIR.
     *
     * 6000  RUTINAS DE IMPRESION DEL REPORTE DE CONTRATOS.           *
     */
    void p6000Imprimir() {
        listado.skipLines(1);
        listado.write(linea);
        if (wsIndicadores.getContLineas() == 60) {
            wsIndicadores.setContLineas(0);

            p6100Enc1();
        }
        //                                                                  07150000
        wsIndicadores.setContLineas(wsIndicadores.getContLineas() + 1);
        linea.setSpaces();
    }

    /**
     *  Paragraph: 6100-ENC1.
     *
     */
    void p6100Enc1() {
        wsIndicadores.setContHojas(wsIndicadores.getContHojas() + 1);
        tit11.getTit11Hoja().setValue(wsIndicadores.getContHojas());
        linea.setLinResto(tit11);
        listado.skipPage();
        listado.write(linea);
        linea.setLinResto(tit21);
        p6000Imprimir();
        //                                                                  07280000
        linea.getLinResto().moveAll("-");
        p6000Imprimir();
    }

    /**
     *  Paragraph: 7100-LEER-GFVTCAF0.
     *
     * LEER LA TABLA  GFVTCAF0  CARTERA AGENTES FACULTAD
     */
    void p7100LeerGfvtcaf0() {
        //                                                                  07311000
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GFVTCAF0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTCAF0        ");

        //                                                                  07311400
        gfvtcaf0Model = 
            gfvtcaf0Port.findByFlaNumAndRamCveAndSraCve(
                                    toHostString(dclgfvtfla0.getFlaNum()),
                    toHostString(dclgfvtfdc0.getRamCve()),
                    toHostString(dclgfvtfdc0.getSraCve()));

        if(gfvtcaf0Model.isPresent()) {
            dclgfvtcaf0.setFlaNum(fromHostString(gfvtcaf0Model.get().flaNum()));
            dclgfvtcaf0.setRamCve(fromHostString(gfvtcaf0Model.get().ramCve()));
            dclgfvtcaf0.setSraCve(fromHostString(gfvtcaf0Model.get().sraCve()));
            dclgfvtcaf0.setFecUltAct(fromHostString(gfvtcaf0Model.get().fecUltAct()));
            dclgfvtcaf0.setUsuCveAct(fromHostString(gfvtcaf0Model.get().usuCveAct()));
        }
        //                                                                  07313800
        db2check();
        //                                                                  07314000
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCaf(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCaf(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7850-INICIO-GFVCFLA0.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTFLA0
     */
    void p7850InicioGfvcfla0() {
        //                                                                  07315700
        // -----> CBA 15/02/2001                                            07315800
        // *   MOVE  '7850-INICIO-GFVCFLA0          ' TO  WS-PROCESO-EJECUTA07315900
        // *   MOVE  'OPEN            '               TO  WS-INSTRUCCION.   07316000
        // *   MOVE  'GFVTFLA0        '               TO  WS-TABLA.         07316100
        //                                                                  07316200
        // *   EXEC  SQL                                                    07316300
        // *         OPEN  GFVCFLA0                                         07316400
        // *   END-EXEC.                                                    07316500
        //                                                                  07316600
        // *   PERFORM  DB2CHECK.                                           07316700
        //                                                                  07316800
        // *   IF  DB2-OK                                                   07316900
        // *       MOVE  0  TO  WS-FIN-FLA                                  07317000
        // *   ELSE                                                         07317100
        // *       PERFORM  9999-ANALIZA-SQL.                               07317200
        //                                                                  07317300
        folios.openInput();
        if (fsFolios > 0) {
            display("ERROR AL ABRIR FOLIOS FS=", fsFolios);
            returnCode.setValue(4);
            throw new StopRunException();
        }
        // <----- CBA                                                       07318000
        p7850LeerGfvtfla0();
        wsIndicadores.setCuantos(wsIndicadores.getCuantos() + 1);
    }

    /**
     *  Paragraph: 7850-LEER-GFVTFLA0.
     *
     * FETCH A GFVTFLA0       FOLIOS
     */
    void p7850LeerGfvtfla0() {
        //                                                                  07319100
        // -----> CBA 15/02/2001                                            07319200
        // *   MOVE  '7850-LEER-GFVTFLA0            ' TO  WS-PROCESO-EJECUTA07319300
        // *   MOVE  'FETCH           '               TO  WS-INSTRUCCION.   07319400
        // *   MOVE  'GFVTFLA0        '               TO  WS-TABLA.         07319500
        //                                                                  07319600
        // *   EXEC  SQL                                                    07319700
        // *         FETCH  GFVCFLA0                                        07319800
        // *          INTO                                                  07319900
        // *               :DCLGFVTFLA0.FLA-NUM                      ,      07320000
        // *               :DCLGFVTFLA0.FLA-DES                      ,      07320100
        // *               :DCLGFVTFLA0.FLA-FEC-INI-ASG :FLA03       ,      07320200
        // *               :DCLGFVTFLA0.FLA-FEC-FIN-ASG :FLA04       ,      07320300
        // *               :DCLGFVTFLA0.OFN-CVE         :FLA05       ,      07320400
        // *               :DCLGFVTFLA0.AGT-IDR                      ,      07320500
        // *               :DCLGFVTFLA0.EMP-CVE                      ,      07320600
        // *               :DCLGFVTFLA0.CNA-NUM                      ,      07320700
        // *               :DCLGFVTFLA0.MTE-CVE                      ,      07320800
        // *               :DCLGFVTFLA0.ORV-CVE                      ,      07320900
        // *               :DCLGFVTFLA0.FLA-GER-ZON     :FLA11       ,      07321000
        // *               :DCLGFVTFLA0.FLA-NEG-CVE     :FLA12       ,      07321100
        // *               :DCLGFVTFLA0.FLA-FEC-MOT-ESS :FLA13              07321200
        //                                                                  07321300
        // *   END-EXEC.                                                    07321400
        //                                                                  07321500
        // *   PERFORM  DB2CHECK.                                           07321600
        //                                                                  07321700
        // *   IF  DB2-OK                                                   07321800
        // *       ADD  1  TO  TOT-FLA-LEIDOS                               07321900
        // *   ELSE                                                         07322000
        // *       IF  DB2-NOTFND                                           07322100
        // *           MOVE  1  TO  WS-FIN-FLA                              07322200
        // *       ELSE                                                     07322300
        // *           PERFORM  9999-ANALIZA-SQL.                           07322400
        //                                                                  07322500
        // *   EXIT.                                                        07322600
        folios.read(tbDclgfvtfla0);
        if (fsFolios == 0) {
            dclgfvtfla0.setFlaNum(tbDclgfvtfla0.getTbFlaNum());
            dclgfvtfla0.setFlaDes(tbDclgfvtfla0.getTbFlaDes());
            dclgfvtfla0.setFlaFecIniAsg(tbDclgfvtfla0.getTbFlaFecIniAsg());
            // -----> AMS 28/03/2001                                            07326100
            if (!tbDclgfvtfla0.getTbFla03().isLowValue()) {
                wsGfvtfla0Null.setFla03(-1);

            }
            // <----- AMS 28/03/2001                                            07326500
            dclgfvtfla0.setFlaFecFinAsg(tbDclgfvtfla0.getTbFlaFecFinAsg());
            // -----> AMS 28/03/2001                                            07328100
            if (!tbDclgfvtfla0.getTbFla04().isLowValue()) {
                wsGfvtfla0Null.setFla04(-1);

            }
            // <----- AMS 28/03/2001                                            07328500
            dclgfvtfla0.setOfnCve(tbDclgfvtfla0.getTbOfnCve());
            // -----> AMS 28/03/2001                                            07331000
            if (!tbDclgfvtfla0.getTbFla05().isLowValue()) {
                wsGfvtfla0Null.setFla05(-1);

            }
            // <----- AMS 28/03/2001                                            07335000
            dclgfvtfla0.setAgtIdr(tbDclgfvtfla0.getTbAgtIdr());
            dclgfvtfla0.setEmpCve(tbDclgfvtfla0.getTbEmpCve());
            dclgfvtfla0.setCnaNum(tbDclgfvtfla0.getTbCnaNum());
            dclgfvtfla0.setMteCve(tbDclgfvtfla0.getTbMteCve());
            dclgfvtfla0.setOrvCve(tbDclgfvtfla0.getTbOrvCve());
            dclgfvtfla0.setFlaGerZon(tbDclgfvtfla0.getTbFlaGerZon());
            // -----> AMS 28/03/2001                                            07451000
            if (!tbDclgfvtfla0.getTbFla11().isLowValue()) {
                wsGfvtfla0Null.setFla11(-1);

            }
            // <----- AMS 28/03/2001                                            07455000
            dclgfvtfla0.setFlaNegCve(tbDclgfvtfla0.getTbFlaNegCve());
            // -----> AMS 28/03/2001                                            07471000
            if (!tbDclgfvtfla0.getTbFla12().isLowValue()) {
                wsGfvtfla0Null.setFla12(-1);

            }
            // <----- AMS 28/03/2001                                            07475000
            dclgfvtfla0.setFlaFecMotEss(tbDclgfvtfla0.getTbFlaFecMotEss());
            // -----> AMS 28/03/2001                                            07480200
            if (!tbDclgfvtfla0.getTbFla13().isLowValue()) {
                wsGfvtfla0Null.setFla13(-1);

            }
            // <----- AMS 28/03/2001                                            07480600
            wsTotales.setTotFlaLeidos(wsTotales.getTotFlaLeidos() + 1);
        } else {
            wsIndicadores.setWsFinFla(1);

        }
    }

    /**
     *  Paragraph: 7850-CIERRO-GFVCFLA0.
     *
     * <----- CBA
     */
    void p7850CierroGfvcfla0() {
        //                                                                  07481700
        // -----> CBA 15/02/2001                                            07481800
        // *   MOVE  '7850-CIERRO-GFVCFLA0          ' TO  WS-PROCESO-EJECUTA07481900
        // *   MOVE  'CLOSE           '               TO  WS-INSTRUCCION.   07482000
        // *   MOVE  'GFVTFLA0        '               TO  WS-TABLA.         07482100
        //                                                                  07482200
        // *   EXEC  SQL                                                    07482300
        // *         CLOSE GFVCFLA0                                         07482400
        // *   END-EXEC.                                                    07482500
        //                                                                  07482600
        // *   PERFORM  DB2CHECK.                                           07482700
        //                                                                  07482800
        // *   IF  DB2-OK                                                   07482900
        // *       NEXT  SENTENCE                                           07483000
        // *   ELSE                                                         07483100
        // *       PERFORM  9999-ANALIZA-SQL.                               07483200
        // *   EXIT.                                                        07483300
        folios.close();
    }

    /**
     *  Paragraph: 7850-INICIO-GFVCFDC0.
     *
     * <----- CBA
     * ABRE CURSOR Y LEE EL PRIMER GFVTFDC0
     */
    void p7850InicioGfvcfdc0() {
        //                                                                  07484900
        wsDatosAbend.setWsProcesoEjecuta("7850-INICIO-GFVCFDC0          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTFDC0        ");

        //                                                                  07485300
        gfvcfdc0Cursor.open();
        //                                                                  07485700
        db2check();
        //                                                                  07485900
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFdc(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  07486400
        // -----> CBA1                                                      07486500
        //     PERFORM  7850-LEER-GFVTFDC0.                                 07486600
        tbdInd = 0;

        while (!(wsIndicadores.getWsFinFdc() == 1)) {
            p7850LeerGfvtfdc0();
        }
    }

    /**
     *  Paragraph: 7850-LEER-GFVTFDC0.
     *
     * <----- CBA1
     * FETCH A GFVTFDC0       CONTRATO-FACULTAD
     */
    void p7850LeerGfvtfdc0() {
        //                                                                  07487800
        wsDatosAbend.setWsProcesoEjecuta("7850-LEER-GFVTFDC0            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTFDC0        ");

        //                                                                  07488200
        gfvtfdc0Model = gfvcfdc0Cursor.next();

        if (gfvtfdc0Model.isPresent()) {
            dclgfvtfdc0.setAgtIdr(fromHostInt(gfvtfdc0Model.get().agtIdr()));
            dclgfvtfdc0.setEmpCve(fromHostString(gfvtfdc0Model.get().empCve()));
            dclgfvtfdc0.setCnaNum(fromHostInt(gfvtfdc0Model.get().cnaNum()));
            dclgfvtfdc0.setRamCve(fromHostString(gfvtfdc0Model.get().ramCve()));
            dclgfvtfdc0.setSraCve(fromHostString(gfvtfdc0Model.get().sraCve()));
        }
        //                                                                  07489300
        db2check();
        //                                                                  07489500
        if (this.isDb2Ok()) {
            wsTotales.setTotFdcLeidos(wsTotales.getTotFdcLeidos() + 1);
            // -----> CBA1                                                      07489800
            tbdInd += 1;
            tbdDclgfvtfdc0.getTbdTabla().at(tbdInd).setTbdAgtIdr(dclgfvtfdc0.getAgtIdr());
            tbdDclgfvtfdc0.getTbdTabla().at(tbdInd).setTbdEmpCve(dclgfvtfdc0.getEmpCve());
            tbdDclgfvtfdc0.getTbdTabla().at(tbdInd).setTbdCnaNum(dclgfvtfdc0.getCnaNum());
            tbdDclgfvtfdc0.getTbdTabla().at(tbdInd).setTbdRamCve(dclgfvtfdc0.getRamCve());
            tbdDclgfvtfdc0.getTbdTabla().at(tbdInd).setTbdSraCve(dclgfvtfdc0.getSraCve());
            if (tbdInd > 5000) {
                display("ERROR EN INDICE TABLA DINAMICA,", " EXCEDE 5000, INCREMENTE Y RECOMPILE");
                getSqlExecutor().rollback(sqlca);
                returnCode.setValue(4);
                throw new StopRunException();
            }
        // <----- CBA1                                                      07491500
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinFdc(1);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  07492100
    }

    /**
     *  Paragraph: 7850-CIERRO-GFVCFDC0.
     *
     */
    void p7850CierroGfvcfdc0() {
        //                                                                  07492700
        wsDatosAbend.setWsProcesoEjecuta("7850-CIERRO-GFVCFDC0          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTFDC0        ");

        //                                                                  07493100
        if (gfvcfdc0Cursor != null) {
            gfvcfdc0Cursor.close();
        }
        //                                                                  07493500
        db2check();
        //                                                                  07493700
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: DB2CHECK.
     *
     * DB2CHECK   RUTINA QUE ANALIZA CODIGO VALIDO.                   *
     */
    void db2check() {
        //                                                                  07495700
        db2ReturnCode = sqlca.getSqlcode();

        //                                                                  07495900
        if (this.isDb2Ok() 
            || this.isDb2Notfnd() 
            || this.isDb2Duprec()) {
            ;
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 9999-ANALIZA-SQL.
     *
     * 9999 RUTINA QUE ANALIZA EL CODIGO DE DB2.                      *
     * *****************ANALIZA*****************************************
     */
    void p9999AnalizaSql() {
        //                                                                  07497400
        wsCodigos.getWsSqlerrml().setValue(sqlca.getSqlerrml());
        //                                                                  07497600
        if (sqlca.getSqlcode() < 0) {
            wsCodigos.setWsSqlcode(Numeric.multiply(sqlca.getSqlcode(), (-1)).toInt());
            wsCodigos.setWsDis1("-");
            wsCodigos.setWsDis2(wsCodigos.getWsSqlcode());
        } else {
            wsCodigos.setWsDis1("+");
            wsCodigos.setWsDis2(wsCodigos.getWsSqlcode());
        }
        //                                                                  07498400
        //                                                                  07498500
        display("============ ERROR DB2 ============");
        display("  ");
        display("CODIGO DE ERROR        ===> ", wsCodigos.getWsCodError());
        display("INSTRUCCION SQL        ===> ", wsDatosAbend.getWsInstruccion());
        display("RUTINA LLAMADORA       ===> ", wsDatosAbend.getWsProcesoLlama());
        display("RUTINA EJECUTA         ===> ", wsDatosAbend.getWsProcesoEjecuta());
        display("TABLA                  ===> ", wsDatosAbend.getWsTabla());
        display("PROGRAMA               ===> ", wsDatosAbend.getWsPrograma());
        display("SQL ERROR LENGTH       ===> ", wsCodigos.getWsSqlerrml());
        display("MENSAJE                ===> ", sqlca.getSqlerrmc());
        getSqlExecutor().rollback(sqlca);
        //                                                                  07499900
        //                                                                  07500000
        if (sqlca.getSqlerrml() > 0) {
            display("VARIABLES ERRONEAS    . . .: ", sqlca.getSqlerrmc());
        }
        if (sqlca.getSqlwarn5().equals("W")) {
            display("MANDATO NO EJECUTADO  . . .: ");
            if (sqlca.getSqlwarn6().equals("W")) {
                display("VALOR DE FECHA AJUSTADO . .: ");
            }
            if (sqlca.getSqlwarn9().equals("W")) {
                display("EXCEPCION ARITMET IGNORADA.: ");
            }
        }
        //                                                                  07501200
        // -----> CBA                                                       07501300
        returnCode.setValue(4);
        // <----- CBA                                                       07501500
        throw new GobackException();
        /* [@WARNING Unreachable code]
        throw new StopRunException();
        */
    }

    /**
     *  Paragraph: 3000-FIN.
     *
     * 30000        FINALES DEL PROCESO                               *
     */
    void p3000Fin() {
        //                                                                  07502800
        for (int idx = 1; idx <= 2; idx++) {
            p6000Imprimir();
        }
        linea.setDet12Letrero("TOT. FLA    LEIDOS ");
        linea.getDet12Total().setValue(wsTotales.getTotFlaLeidos());
        p6000Imprimir();
        //                                                                  07503300
        linea.setDet12Letrero("TOT. FDC    LEIDOS ");
        linea.getDet12Total().setValue(wsTotales.getTotFdcLeidos());
        p6000Imprimir();
        //                                                                  07503700
        linea.setDet12Letrero("TOT. CAF    LEIDOS ");
        linea.getDet12Total().setValue(wsTotales.getTotCafLeidos());
        p6000Imprimir();
        //                                                                  07504100
        linea.setDet12Letrero("TOT. CAF    ALTAS  ");
        linea.getDet12Total().setValue(wsTotales.getTotCafGrabados());
        p6000Imprimir();
        //                                                                  07504500
        linea.setDet12Letrero("TOT. CAF    DUPL.  ");
        linea.getDet12Total().setValue(wsTotales.getTotCafDuplicados());
        p6000Imprimir();
        //                                                                  07504900
        listado.close();
    }

    // Conditionals (88) - DB2-RETURN-CODE
    public boolean isDb2Ok() {
        return this.db2ReturnCode == DB2_OK;
    }

    public void setDb2Ok() {
        this.db2ReturnCode = DB2_OK;
    }

    public boolean isDb2Notfnd() {
        return this.db2ReturnCode == DB2_NOTFND;
    }

    public void setDb2Notfnd() {
        this.db2ReturnCode = DB2_NOTFND;
    }

    public boolean isDb2Duprec() {
        return this.db2ReturnCode == DB2_DUPREC;
    }

    public void setDb2Duprec() {
        this.db2ReturnCode = DB2_DUPREC;
    }

    public boolean isDb2Notuni() {
        return this.db2ReturnCode == DB2_NOTUNI;
    }

    public void setDb2Notuni() {
        this.db2ReturnCode = DB2_NOTUNI;
    }

    public boolean isDb2Datetime() {
        return this.db2ReturnCode == DB2_DATETIME;
    }

    public void setDb2Datetime() {
        this.db2ReturnCode = DB2_DATETIME;
    }

    public boolean isDb2System() {
        return this.db2ReturnCode == DB2_SYSTEM;
    }

    public void setDb2System() {
        this.db2ReturnCode = DB2_SYSTEM;
    }



    @Autowired
    public void setGfvtfdc0Port(IGfvtfdc0Port gfvtfdc0Port) {
        this.gfvtfdc0Port = gfvtfdc0Port;
        this.gfvtfdc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcaf0Port(IGfvtcaf0Port gfvtcaf0Port) {
        this.gfvtcaf0Port = gfvtcaf0Port;
        this.gfvtcaf0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setSysdummy1Port(ISysdummy1Port sysdummy1Port) {
        this.sysdummy1Port = sysdummy1Port;
        this.sysdummy1Port.setProgramContext(getProgramContext());
    }
}
